#!/usr/bin/env Rscript
#' Force Reload and Test QSP Model Parameters
#' ==========================================
#'
#' This script forces a complete reload of the QSP model
#' and verifies that the maximally aggressive parameters
#' are being used correctly.

# STEP 1: Complete environment cleanup
cat("STEP 1: Cleaning R environment...\n")
rm(list = ls())
gc()

# Clear any loaded packages (except base packages)
loaded_packages <- search()
for (pkg in loaded_packages) {
  if (grepl("package:", pkg) && !grepl("base|utils|datasets|methods|graphics|grDevices|stats", pkg)) {
    try(detach(pkg, character.only = TRUE, unload = TRUE), silent = TRUE)
  }
}

# STEP 2: Fresh library loading
cat("STEP 2: Loading required libraries...\n")
suppressMessages({
  library(R6)
  library(deSolve)
  library(dplyr)
  library(futile.logger)
})

# STEP 3: Force source all files
cat("STEP 3: Force sourcing QSP model files...\n")
source("R/qsp_model_core.R", local = FALSE)
source("R/qsp_population_analysis.R", local = FALSE)

# STEP 4: Create fresh model instance and verify parameters
cat("STEP 4: Creating fresh model instance...\n")
model <- QSPModel$new()

cat("PARAMETER VERIFICATION:\n")
cat("=======================\n")
cat("k_death =", model$params$k_death, "(expected: 0.0002)\n")
cat("epsilon =", model$params$epsilon, "(expected: 0.01)\n")
cat("EC50_IFN_death =", model$params$EC50_IFN_death, "(expected: 2000)\n")
cat("k_clear_IFN =", model$params$k_clear_IFN, "(expected: 50.0)\n")
cat("cytokine_threshold_pg_ml =", model$params$cytokine_threshold_pg_ml, "(expected: 5000.0)\n")

# STEP 5: Test susceptibility assignment
cat("\nSUSCEPTIBILITY RATE VERIFICATION:\n")
cat("=================================\n")

# Test susceptibility rates for each drug
drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
expected_rates <- c(0.02, 0.015, 0.01, 0.008)

for (i in seq_along(drugs)) {
  drug <- drugs[i]
  expected <- expected_rates[i]
  
  # Test with 1000 assignments to estimate rate
  susceptible_count <- 0
  for (j in 1:1000) {
    test_model <- QSPModel$new()
    test_model$assign_immune_susceptibility(drug)
    if (test_model$params$immune_susceptible) {
      susceptible_count <- susceptible_count + 1
    }
  }
  
  observed_rate <- susceptible_count / 1000
  cat(drug, ": observed =", round(observed_rate, 4), ", expected =", expected, "\n")
}

# STEP 6: Quick simulation test
cat("\nQUICK SIMULATION TEST:\n")
cat("======================\n")

# Test one patient per drug
for (drug in drugs) {
  cat("Testing", drug, "...\n")
  
  test_model <- QSPModel$new()
  test_model$assign_immune_susceptibility(drug)
  test_model$assign_covariates()
  test_model$apply_covariate_effects()
  
  # Check final parameter values after covariate effects
  cat("  Final k_death:", test_model$params$k_death, "\n")
  cat("  Final epsilon:", test_model$params$epsilon, "\n")
  cat("  Susceptible:", test_model$params$immune_susceptible, "\n")
  cat("  Susceptibility level:", test_model$params$susceptibility_level, "\n")
  
  # Run short simulation
  result <- test_model$simulate(
    drug_type = drug,
    t_span = c(0, 84),  # 12 weeks
    save_timeseries = FALSE
  )
  
  cat("  Final TSH:", round(result$final_TSH, 2), "mIU/L\n")
  cat("  Any hypothyroidism:", result$any_hypothyroidism, "\n")
  cat("  Grade 2+ hypothyroidism:", result$grade2_hypothyroidism, "\n\n")
}

# STEP 7: Small population test
cat("SMALL POPULATION TEST (n=20):\n")
cat("==============================\n")

set.seed(42)
cohort <- VirtualCohort$new(n_patients = 20, random_state = 42)
results <- cohort$simulate_all(
  t_span = c(0, 168),
  save_timeseries = FALSE,
  parallel = FALSE
)

any_rate <- mean(results$any_hypothyroidism) * 100
grade2_rate <- mean(results$grade2_hypothyroidism) * 100

cat("Results (n=20):\n")
cat("- Any hypothyroidism:", round(any_rate, 1), "%\n")
cat("- Grade 2+ hypothyroidism:", round(grade2_rate, 1), "%\n")

# Drug breakdown
drug_summary <- results %>%
  group_by(drug_type) %>%
  summarise(
    n = n(),
    any_rate = mean(any_hypothyroidism) * 100,
    grade2_rate = mean(grade2_hypothyroidism) * 100,
    .groups = 'drop'
  )

cat("\nDrug-specific rates:\n")
for (i in 1:nrow(drug_summary)) {
  row <- drug_summary[i, ]
  cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
      round(row$any_rate, 1), "% any,", 
      round(row$grade2_rate, 1), "% grade 2+\n")
}

# STEP 8: Assessment
cat("\nASSESSMENT:\n")
cat("===========\n")

if (any_rate <= 30 && grade2_rate <= 25) {
  cat("✅ SUCCESS: Parameters are working! Rates significantly reduced.\n")
  cat("Recommendation: Proceed with larger population test.\n")
} else {
  cat("❌ ISSUE: Rates still too high even with maximally aggressive parameters.\n")
  cat("This suggests a fundamental issue with the model structure or parameter application.\n")
  
  if (any_rate > 50) {
    cat("🚨 CRITICAL: Rates barely changed from original 58%/52%\n")
    cat("Possible causes:\n")
    cat("1. Parameters being overridden somewhere in the code\n")
    cat("2. Model structure issue preventing parameter effects\n")
    cat("3. Caching or sourcing problem\n")
    cat("4. Different parameter set being used in simulation\n")
  }
}

cat("\nForce reload test completed at:", format(Sys.time()), "\n")
