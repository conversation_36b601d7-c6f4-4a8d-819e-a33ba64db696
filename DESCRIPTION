Package: QSPThyroid
Type: Package
Title: Quantitative Systems Pharmacology Model for Checkpoint-Inhibitor-Induced Hypothyroidism
Version: 1.0.0
Date: 2024-07-25
Authors@R: c(
    person("QSP Modeling Team", email = "<EMAIL>", role = c("aut", "cre")),
    person("Augment Agent", email = "<EMAIL>", role = "ctb"))
Description: A comprehensive quantitative systems pharmacology (QSP) model for predicting 
    hypothyroidism induced by PD-1/PD-L1 checkpoint inhibitors. The package implements a 
    6-ODE system representing immune activation, cytokine release, thyrocyte damage, and 
    HPT axis feedback. Includes patient-specific covariates for personalized risk assessment, 
    drug-specific pharmacokinetic models for different ICIs, population analysis tools, 
    and model calibration methods.
License: MIT + file LICENSE
URL: https://github.com/qsp-team/QSPThyroid
BugReports: https://github.com/qsp-team/QSPThyroid/issues
Depends: 
    R (>= 4.0.0)
Imports:
    R6 (>= 2.5.0),
    deSolve (>= 1.28),
    data.table (>= 1.14.0),
    ggplot2 (>= 3.3.0),
    dplyr (>= 1.0.0),
    parallel,
    future (>= 1.21.0),
    future.apply (>= 1.8.0),
    argparse (>= 2.0.0),
    futile.logger (>= 1.4.0),
    rmarkdown (>= 2.0.0),
    knitr (>= 1.30.0),
    survival (>= 3.2.0),
    BayesianTools (>= 0.1.7),
    coda (>= 0.19.0),
    MASS (>= 7.3.0),
    Matrix (>= 1.3.0),
    pracma (>= 2.3.0)
Suggests:
    testthat (>= 3.0.0),
    covr,
    pkgdown,
    devtools,
    roxygen2 (>= 7.0.0),
    spelling,
    lintr,
    styler
Encoding: UTF-8
LazyData: true
RoxygenNote: 7.2.3
VignetteBuilder: knitr
Language: en-US
