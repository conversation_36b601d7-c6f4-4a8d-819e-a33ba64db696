import random
from qsp_model_core import QSPMode<PERSON>, simulate_patient, ModelParameters
import pandas as pd

# Debug why non-susceptible patients are developing hypothyroidism
print('Debugging non-susceptible hypothyroidism:')
print('='*60)

# Use seed 42 to reproduce Patient 4 and Patient 7 from the fixed seed test
random.seed(42)

# Simulate the exact scenario from the population test
# Patient 4 was the 4th patient, nivolumab, non-susceptible, TSH: 7.81

# Create a non-susceptible patient
params = ModelParameters()
model = QSPModel(params)

# Force non-susceptible assignment (like Patient 4)
model.params.immune_susceptible = False
model.params.susceptibility_level = "none"
model.params.susceptibility_assigned = True
model._apply_covariates()

print(f'Patient parameters:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_level: {model.params.susceptibility_level}')
print(f'  k_death: {model.params.k_death}')
print(f'  epsilon: {model.params.epsilon}')

# Simulate with nivolumab
drug_type = 'nivolumab'
results = simulate_patient(model, t_span=(0, 168), drug_type=drug_type)

print(f'\nSimulation results:')
print(f'  Final TSH: {results.iloc[-1]["TSH"]:.2f} mIU/L')
print(f'  Final IFN: {results.iloc[-1]["IFN"]:.1f} pg/mL')
print(f'  Final Thyro: {results.iloc[-1]["Thyro"]:.3f}')
print(f'  Final drug: {results.iloc[-1]["drug_concentration"]:.1f} ng/mL')

# Check time course
print(f'\nTime course analysis:')
print(f'  Max IFN: {results["IFN"].max():.1f} pg/mL')
print(f'  Max drug: {results["drug_concentration"].max():.1f} ng/mL')
print(f'  Min Thyro: {results["Thyro"].min():.3f}')

# Check if there's any IFN production
ifn_nonzero = results[results['IFN'] > 0.01]
if len(ifn_nonzero) > 0:
    print(f'  ERROR: Non-susceptible patient produced IFN-γ!')
    print(f'  First IFN production at day {ifn_nonzero.iloc[0]["time"]:.0f}')
    print(f'  Drug concentration at that time: {ifn_nonzero.iloc[0]["drug_concentration"]:.1f} ng/mL')
    
    # Check activation threshold
    drug_thresholds = {
        'nivolumab': 45000.0,
        'pembrolizumab': 30000.0,
        'atezolizumab': 200000.0,
        'durvalumab': 150000.0
    }
    threshold = drug_thresholds[drug_type]
    print(f'  Activation threshold: {threshold:.0f} ng/mL')
    print(f'  Above threshold: {ifn_nonzero.iloc[0]["drug_concentration"] >= threshold}')
else:
    print(f'  Good: No IFN-γ production detected')

# Check thyrocyte dynamics
thyro_change = results[results['Thyro'] < 0.999]
if len(thyro_change) > 0:
    print(f'  ERROR: Thyrocyte mass decreased without IFN-γ!')
    print(f'  First thyrocyte loss at day {thyro_change.iloc[0]["time"]:.0f}')
    print(f'  Thyro value: {thyro_change.iloc[0]["Thyro"]:.6f}')
    print(f'  IFN at that time: {thyro_change.iloc[0]["IFN"]:.6f} pg/mL')
else:
    print(f'  Good: No thyrocyte loss detected')

# Check if TSH is actually elevated
if results.iloc[-1]["TSH"] > 4.5:
    print(f'  ERROR: TSH elevated without thyrocyte loss!')
    print(f'  This suggests a bug in the TSH feedback calculation')
    
    # Check T3 levels
    final_T3 = results.iloc[-1]["T3"]
    print(f'  Final T3: {final_T3:.2f} pmol/L')
    
    # Check if T3 is low (which would cause high TSH)
    if final_T3 < 3.1:
        print(f'  T3 is low, which explains high TSH')
        print(f'  But why is T3 low if Thyro mass is normal?')
    else:
        print(f'  T3 is normal, so TSH should not be elevated')
        print(f'  This indicates a bug in the TSH calculation')

print(f'\nDetailed final state:')
final_state = results.iloc[-1]
for col in ['TSH', 'T3', 'Thyro', 'IFN', 'drug_concentration', 'R', 'T_eff']:
    if col in final_state:
        print(f'  {col}: {final_state[col]:.6f}')
