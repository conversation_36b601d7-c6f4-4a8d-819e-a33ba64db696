#' QSP Thyroid Model - Comprehensive Plotting Functions
#'
#' This module provides publication-quality plotting functions for the QSP thyroid model
#' with calibrated parameters (k_death=0.030, EC50_IFN_death=100) that produce realistic
#' hypothyroidism incidence rates (0-15%).
#'
#' <AUTHOR> Modeling Team

# Load required libraries
suppressMessages({
  library(ggplot2)
  library(dplyr)
  library(gridExtra)
  library(scales)
  library(viridis)
  library(tidyr)
})

# Define consistent color palette (matching Python version)
COLORS <- list(
  tsh = "#2E86AB",      # Blue for TSH
  t3 = "#A23B72",       # Purple for T3
  drug = "#F18F01",     # Orange for drug concentrations
  cytokine = "#C73E1D", # Red for cytokines
  thyrocyte = "#4CAF50", # Green for thyrocytes
  grade1 = "#FFC107",   # Amber for Grade 1
  grade2 = "#FF5722",   # Deep orange for Grade 2+
  normal = "#4CAF50"    # Green for normal
)

# Set ggplot2 theme for publication quality
theme_qsp <- function() {
  theme_minimal() +
    theme(
      text = element_text(family = "Arial", size = 12),
      plot.title = element_text(size = 16, face = "bold", hjust = 0.5, margin = margin(b = 20)),
      axis.title = element_text(size = 14, face = "bold"),
      axis.text = element_text(size = 12),
      legend.title = element_text(size = 12, face = "bold"),
      legend.text = element_text(size = 10),
      panel.grid.minor = element_blank(),
      panel.grid.major = element_line(colour = "grey90"),
      strip.text = element_text(size = 12, face = "bold")
    )
}

#' Plot TSH progression with CTCAE grade thresholds
#'
#' @param results Data frame or list containing time and TSH columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @param show_grades Whether to show CTCAE grade threshold lines
#' @return ggplot object
#' @export
plot_tsh_timecourse <- function(results, patient_id = NULL, save_path = NULL, show_grades = TRUE) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    df <- data.frame(
      time = results$time,
      TSH = results$TSH
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
  }
  
  # Convert time to weeks
  df$time_weeks <- df$time / 7
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = TSH)) +
    geom_line(color = COLORS$tsh, linewidth = 1.5) +
    labs(
      x = "Time (weeks)",
      y = "TSH (mIU/L)",
      title = if (is.null(patient_id)) "TSH Progression Over Time" else paste("TSH Progression Over Time - Patient", patient_id)
    ) +
    theme_qsp()
  
  if (show_grades) {
    # Add CTCAE grade threshold lines
    p <- p +
      geom_hline(yintercept = 4.5, color = COLORS$grade1, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      geom_hline(yintercept = 10.0, color = COLORS$grade2, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      annotate("rect", xmin = -Inf, xmax = Inf, ymin = 0.4, ymax = 4.5, 
               alpha = 0.1, fill = COLORS$normal) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 4.5, 
               label = "Grade 1 threshold (4.5 mIU/L)", vjust = -0.5, size = 3) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 10.0, 
               label = "Grade 2 threshold (10.0 mIU/L)", vjust = -0.5, size = 3)
  }
  
  # Set reasonable y-axis limits
  max_tsh <- max(df$TSH, na.rm = TRUE)
  p <- p + ylim(0, max(12, max_tsh * 1.1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot T3 levels with normal range and hypothyroidism threshold
#'
#' @param results Data frame or list containing time and T3 columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @param show_normal_range Whether to show normal T3 range
#' @return ggplot object
#' @export
plot_t3_timecourse <- function(results, patient_id = NULL, save_path = NULL, show_normal_range = TRUE) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    df <- data.frame(
      time = results$time,
      T3 = results$T3
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
  }
  
  # Convert time to weeks
  df$time_weeks <- df$time / 7
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = T3)) +
    geom_line(color = COLORS$t3, linewidth = 1.5) +
    labs(
      x = "Time (weeks)",
      y = "T3 (pmol/L)",
      title = if (is.null(patient_id)) "T3 Levels Over Time" else paste("T3 Levels Over Time - Patient", patient_id)
    ) +
    theme_qsp()
  
  if (show_normal_range) {
    # Normal T3 range: 3.1-6.8 pmol/L
    p <- p +
      annotate("rect", xmin = -Inf, xmax = Inf, ymin = 3.1, ymax = 6.8, 
               alpha = 0.1, fill = COLORS$normal) +
      geom_hline(yintercept = 3.1, color = COLORS$grade1, linetype = "dashed", alpha = 0.7, linewidth = 1) +
      annotate("text", x = max(df$time_weeks) * 0.8, y = 3.1, 
               label = "Hypothyroidism threshold (<3.1 pmol/L)", vjust = 1.2, size = 3) +
      annotate("text", x = max(df$time_weeks) * 0.2, y = 5.0, 
               label = "Normal range (3.1-6.8 pmol/L)", size = 3)
  }
  
  # Set reasonable y-axis limits
  min_t3 <- min(df$T3, na.rm = TRUE)
  max_t3 <- max(df$T3, na.rm = TRUE)
  p <- p + ylim(max(0, min_t3 * 0.9), max_t3 * 1.1)
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot drug concentration over treatment cycles with therapeutic thresholds
#'
#' @param results Data frame or list containing time and drug concentration
#' @param drug_type Type of checkpoint inhibitor drug
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_drug_pk_profile <- function(results, drug_type = "nivolumab", patient_id = NULL, save_path = NULL) {
  
  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    drug_conc <- if ("drug_concentration" %in% names(results)) {
      results$drug_concentration
    } else if ("C_drug" %in% names(results)) {
      results$C_drug
    } else {
      rep(0, length(results$time))
    }
    
    df <- data.frame(
      time = results$time,
      drug_concentration = drug_conc
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"drug_concentration" %in% names(df)) {
      if ("C_drug" %in% names(df)) {
        df$drug_concentration <- df$C_drug
      } else {
        df$drug_concentration <- 0
      }
    }
  }
  
  # Convert time to weeks and concentration to μg/mL
  df$time_weeks <- df$time / 7
  df$drug_conc_ug <- df$drug_concentration / 1000  # ng/mL to μg/mL
  
  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = drug_conc_ug)) +
    geom_line(color = COLORS$drug, linewidth = 1.5) +
    geom_hline(yintercept = 50, color = "red", linetype = "dashed", alpha = 0.7, linewidth = 1) +
    labs(
      x = "Time (weeks)",
      y = "Drug Concentration (μg/mL)",
      title = if (is.null(patient_id)) {
        paste(tools::toTitleCase(drug_type), "Pharmacokinetic Profile")
      } else {
        paste(tools::toTitleCase(drug_type), "Pharmacokinetic Profile - Patient", patient_id)
      }
    ) +
    theme_qsp()
  
  # Add dosing schedule markers (every 2 weeks)
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  dosing_weeks <- seq(0, max_weeks, by = 2)
  for (week in dosing_weeks) {
    p <- p + geom_vline(xintercept = week, color = "gray", linetype = "dotted", alpha = 0.5)
  }
  
  # Add threshold annotation
  p <- p + annotate("text", x = max_weeks * 0.7, y = 55, 
                   label = "Activation threshold (50 μg/mL)", size = 3)
  
  # Set y-axis limits
  max_conc <- max(df$drug_conc_ug, na.rm = TRUE)
  p <- p + ylim(0, max(100, max_conc * 1.1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}

#' Plot IFN-γ levels with activation threshold
#'
#' @param results Data frame or list containing time and IFN_gamma columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_cytokine_response <- function(results, patient_id = NULL, save_path = NULL) {

  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    ifn <- if ("IFN_gamma" %in% names(results)) {
      results$IFN_gamma
    } else if ("IFN" %in% names(results)) {
      results$IFN
    } else {
      rep(0, length(results$time))
    }

    df <- data.frame(
      time = results$time,
      IFN_gamma = ifn
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"IFN_gamma" %in% names(df)) {
      if ("IFN" %in% names(df)) {
        df$IFN_gamma <- df$IFN
      } else {
        df$IFN_gamma <- 0
      }
    }
  }

  # Convert time to weeks
  df$time_weeks <- df$time / 7

  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = IFN_gamma)) +
    geom_line(color = COLORS$cytokine, linewidth = 1.5) +
    geom_hline(yintercept = 100, color = "red", linetype = "dashed", alpha = 0.7, linewidth = 1) +
    labs(
      x = "Time (weeks)",
      y = "IFN-γ (pg/mL)",
      title = if (is.null(patient_id)) "Cytokine Response (IFN-γ) Over Time" else paste("Cytokine Response (IFN-γ) Over Time - Patient", patient_id)
    ) +
    theme_qsp()

  # Add threshold annotation
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  p <- p + annotate("text", x = max_weeks * 0.7, y = 110,
                   label = "Activation threshold (100 pg/mL)", size = 3)

  # Set y-axis limits
  max_ifn <- max(df$IFN_gamma, na.rm = TRUE)
  p <- p + ylim(0, max(200, max_ifn * 1.1))

  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}

#' Plot thyrocyte mass percentage over time
#'
#' @param results Data frame or list containing time and Thyro columns
#' @param patient_id Optional patient ID for title
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_thyrocyte_depletion <- function(results, patient_id = NULL, save_path = NULL) {

  # Extract data
  if (is.list(results) && !is.data.frame(results)) {
    thyro <- if ("Thyro" %in% names(results)) {
      results$Thyro
    } else if ("thyrocyte_mass" %in% names(results)) {
      results$thyrocyte_mass
    } else {
      rep(1, length(results$time))
    }

    df <- data.frame(
      time = results$time,
      Thyro = thyro
    )
  } else {
    df <- results
    if (!"time" %in% names(df)) {
      df$time <- seq_len(nrow(df))
    }
    if (!"Thyro" %in% names(df)) {
      if ("thyrocyte_mass" %in% names(df)) {
        df$Thyro <- df$thyrocyte_mass
      } else {
        df$Thyro <- 1
      }
    }
  }

  # Convert time to weeks and thyrocyte mass to percentage
  df$time_weeks <- df$time / 7
  df$thyro_percent <- df$Thyro * 100

  # Create base plot
  p <- ggplot(df, aes(x = time_weeks, y = thyro_percent)) +
    geom_line(color = COLORS$thyrocyte, linewidth = 1.5) +
    geom_hline(yintercept = 100, color = "gray", linetype = "solid", alpha = 0.5, size = 1) +
    geom_hline(yintercept = 80, color = "orange", linetype = "dashed", alpha = 0.7, size = 1) +
    geom_hline(yintercept = 50, color = "red", linetype = "dashed", alpha = 0.7, size = 1) +
    labs(
      x = "Time (weeks)",
      y = "Thyrocyte Mass (%)",
      title = if (is.null(patient_id)) "Thyrocyte Mass Depletion Over Time" else paste("Thyrocyte Mass Depletion Over Time - Patient", patient_id)
    ) +
    theme_qsp()

  # Add reference line annotations
  max_weeks <- max(df$time_weeks, na.rm = TRUE)
  p <- p +
    annotate("text", x = max_weeks * 0.8, y = 100, label = "Baseline (100%)", vjust = -0.5, size = 3) +
    annotate("text", x = max_weeks * 0.8, y = 80, label = "Mild depletion (80%)", vjust = -0.5, size = 3) +
    annotate("text", x = max_weeks * 0.8, y = 50, label = "Severe depletion (50%)", vjust = -0.5, size = 3)

  # Set y-axis limits
  min_thyro <- min(df$thyro_percent, na.rm = TRUE)
  p <- p + ylim(max(0, min_thyro * 0.9), 105)

  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }

  return(p)
}

#' Plot incidence rates by drug type with confidence intervals
#'
#' @param population_results Data frame with drug_type and any_hypothyroidism columns
#' @param save_path Optional path to save the plot
#' @param show_ci Whether to show confidence intervals
#' @return ggplot object
#' @export
plot_incidence_by_drug <- function(population_results, save_path = NULL, show_ci = TRUE) {
  
  # Calculate incidence rates by drug
  drug_incidence <- population_results %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      grade2_rate = mean(grade2_hypothyroidism, na.rm = TRUE) * 100,
      any_se = sqrt(any_rate * (100 - any_rate) / n),
      grade2_se = sqrt(grade2_rate * (100 - grade2_rate) / n),
      .groups = 'drop'
    )
  
  # Reshape for plotting
  plot_data <- drug_incidence %>%
    select(drug_type, any_rate, grade2_rate, any_se, grade2_se) %>%
    pivot_longer(
      cols = c(any_rate, grade2_rate),
      names_to = "severity",
      values_to = "incidence"
    ) %>%
    mutate(
      se = ifelse(severity == "any_rate", any_se, grade2_se),
      severity = factor(severity, levels = c("any_rate", "grade2_rate"),
                       labels = c("Any Hypothyroidism", "Grade 2+ Hypothyroidism"))
    )
  
  # Create plot
  p <- ggplot(plot_data, aes(x = drug_type, y = incidence, fill = severity)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    scale_fill_manual(values = c("Any Hypothyroidism" = COLORS$grade1, 
                                "Grade 2+ Hypothyroidism" = COLORS$grade2)) +
    labs(
      x = "Drug Type",
      y = "Incidence Rate (%)",
      title = "Hypothyroidism Incidence by Drug Type",
      fill = "Severity"
    ) +
    theme_qsp() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  if (show_ci) {
    p <- p + geom_errorbar(
      aes(ymin = incidence - 1.96 * se, ymax = incidence + 1.96 * se),
      position = position_dodge(width = 0.8), width = 0.25
    )
  }
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot onset time distribution for hypothyroidism cases
#'
#' @param population_results Data frame with time_to_onset_days column
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_onset_distribution <- function(population_results, save_path = NULL) {
  
  # Filter for cases with onset times
  onset_data <- population_results %>%
    filter(!is.na(time_to_onset_days) & time_to_onset_days > 0)
  
  if (nrow(onset_data) == 0) {
    warning("No onset time data available for plotting")
    return(NULL)
  }
  
  # Convert to weeks
  onset_data$onset_weeks <- onset_data$time_to_onset_days / 7
  
  # Create plot
  p <- ggplot(onset_data, aes(x = onset_weeks)) +
    geom_histogram(binwidth = 2, fill = COLORS$grade2, alpha = 0.7, color = "white") +
    geom_density(aes(y = ..count.. * 2), color = COLORS$grade2, linewidth = 1.5) +
    labs(
      x = "Time to Onset (weeks)",
      y = "Number of Cases",
      title = "Distribution of Hypothyroidism Onset Times"
    ) +
    theme_qsp()
  
  # Add vertical line for median
  median_weeks <- median(onset_data$onset_weeks, na.rm = TRUE)
  p <- p + geom_vline(xintercept = median_weeks, color = "red", 
                      linetype = "dashed", linewidth = 1) +
    annotate("text", x = median_weeks + 2, y = max(ggplot_build(p)$data[[1]]$count) * 0.8,
             label = paste("Median:", round(median_weeks, 1), "weeks"), 
             color = "red", size = 4)
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot risk stratification by patient characteristics
#'
#' @param population_results Data frame with patient characteristics and outcomes
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_risk_stratification <- function(population_results, save_path = NULL) {
  
  # Calculate risk by age groups
  age_risk <- population_results %>%
    mutate(age_group = cut(age, breaks = c(0, 50, 65, 80, 100), 
                           labels = c("<50", "50-65", "65-80", ">80"))) %>%
    group_by(age_group) %>%
    summarise(
      n = n(),
      incidence = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Calculate risk by sex
  sex_risk <- population_results %>%
    group_by(sex) %>%
    summarise(
      n = n(),
      incidence = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Calculate risk by HLA status
  hla_risk <- population_results %>%
    group_by(HLA_DRB1_03) %>%
    summarise(
      n = n(),
      incidence = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Combine all risk factors
  risk_data <- bind_rows(
    age_risk %>% mutate(factor = "Age Group", level = as.character(age_group)),
    sex_risk %>% mutate(factor = "Sex", level = sex),
    hla_risk %>% mutate(factor = "HLA-DRB1*03", level = ifelse(HLA_DRB1_03, "Positive", "Negative"))
  )
  
  # Create plot
  p <- ggplot(risk_data, aes(x = level, y = incidence, fill = factor)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    scale_fill_viridis_d() +
    labs(
      x = "Risk Factor Level",
      y = "Incidence Rate (%)",
      title = "Risk Stratification by Patient Characteristics",
      fill = "Risk Factor"
    ) +
    theme_qsp() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 14, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot dose-response relationship
#'
#' @param population_results Data frame with drug concentration and outcomes
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_dose_response <- function(population_results, save_path = NULL) {
  
  # Calculate average drug concentration by drug type
  dose_response <- population_results %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      avg_conc = mean(peak_drug_concentration, na.rm = TRUE),
      incidence = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Create plot
  p <- ggplot(dose_response, aes(x = avg_conc, y = incidence, color = drug_type)) +
    geom_point(size = 4) +
    geom_smooth(method = "lm", se = TRUE, alpha = 0.2) +
    scale_color_viridis_d() +
    labs(
      x = "Average Peak Drug Concentration (μg/mL)",
      y = "Incidence Rate (%)",
      title = "Dose-Response Relationship",
      color = "Drug Type"
    ) +
    theme_qsp()
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot literature comparison
#'
#' @param model_results Data frame with model predictions
#' @param literature_data Optional data frame with literature values
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_literature_comparison <- function(model_results, literature_data = NULL, save_path = NULL) {
  
  # Calculate model incidence by drug
  model_incidence <- model_results %>%
    group_by(drug_type) %>%
    summarise(
      incidence = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Literature values (if not provided, use typical ranges)
  if (is.null(literature_data)) {
    literature_data <- data.frame(
      drug_type = c("nivolumab", "pembrolizumab", "atezolizumab", "durvalumab"),
      lit_incidence = c(8.0, 7.5, 6.0, 9.0),
      lit_min = c(5.0, 4.5, 3.0, 6.0),
      lit_max = c(12.0, 11.0, 9.0, 13.0)
    )
  }
  
  # Combine data
  comparison_data <- model_incidence %>%
    left_join(literature_data, by = "drug_type")
  
  # Create plot
  p <- ggplot(comparison_data, aes(x = drug_type)) +
    geom_point(aes(y = incidence, color = "Model"), size = 4) +
    geom_point(aes(y = lit_incidence, color = "Literature"), size = 4, shape = 17) +
    geom_errorbar(aes(ymin = lit_min, ymax = lit_max, color = "Literature"), 
                  width = 0.2, linewidth = 1) +
    scale_color_manual(values = c("Model" = COLORS$grade2, "Literature" = COLORS$tsh)) +
    labs(
      x = "Drug Type",
      y = "Incidence Rate (%)",
      title = "Model vs Literature Comparison",
      color = "Source"
    ) +
    theme_qsp() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot CTCAE validation
#'
#' @param population_results Data frame with CTCAE grade data
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_ctcae_validation <- function(population_results, save_path = NULL) {
  
  # Calculate CTCAE grade distribution
  ctcae_data <- population_results %>%
    mutate(
      ctcae_grade = case_when(
        peak_TSH_mIU_per_L < 4.5 ~ "Normal",
        peak_TSH_mIU_per_L < 10.0 ~ "Grade 1",
        peak_TSH_mIU_per_L < 20.0 ~ "Grade 2",
        TRUE ~ "Grade 3+"
      )
    ) %>%
    group_by(drug_type, ctcae_grade) %>%
    summarise(
      n = n(),
      .groups = 'drop'
    ) %>%
    group_by(drug_type) %>%
    mutate(percentage = n / sum(n) * 100)
  
  # Create plot
  p <- ggplot(ctcae_data, aes(x = drug_type, y = percentage, fill = ctcae_grade)) +
    geom_bar(stat = "identity", position = "stack") +
    scale_fill_manual(values = c("Normal" = COLORS$normal, 
                                "Grade 1" = COLORS$grade1,
                                "Grade 2" = COLORS$grade2,
                                "Grade 3+" = "#D32F2F")) +
    labs(
      x = "Drug Type",
      y = "Percentage of Patients (%)",
      title = "CTCAE Grade Distribution by Drug Type",
      fill = "CTCAE Grade"
    ) +
    theme_qsp() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Plot sensitivity analysis
#'
#' @param sensitivity_results Data frame with sensitivity analysis results
#' @param save_path Optional path to save the plot
#' @return ggplot object
#' @export
plot_sensitivity_analysis <- function(sensitivity_results, save_path = NULL) {
  
  # Create tornado plot
  p <- ggplot(sensitivity_results, aes(x = parameter, y = sensitivity)) +
    geom_bar(stat = "identity", fill = COLORS$grade2, alpha = 0.7) +
    coord_flip() +
    labs(
      x = "Parameter",
      y = "Sensitivity Index",
      title = "Parameter Sensitivity Analysis"
    ) +
    theme_qsp()
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 12, height = 8, dpi = 300)
  }
  
  return(p)
}

#' Save all population plots
#'
#' @param population_results Data frame with population simulation results
#' @param output_dir Directory to save plots
#' @return List of saved plot paths
#' @export
save_all_population_plots <- function(population_results, output_dir = "population_plots") {
  
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  saved_plots <- list()
  
  # Incidence by drug
  p1 <- plot_incidence_by_drug(population_results, 
                               save_path = file.path(output_dir, "incidence_by_drug.png"))
  saved_plots$incidence_by_drug <- file.path(output_dir, "incidence_by_drug.png")
  
  # Onset distribution
  p2 <- plot_onset_distribution(population_results, 
                                save_path = file.path(output_dir, "onset_distribution.png"))
  saved_plots$onset_distribution <- file.path(output_dir, "onset_distribution.png")
  
  # Risk stratification
  p3 <- plot_risk_stratification(population_results, 
                                 save_path = file.path(output_dir, "risk_stratification.png"))
  saved_plots$risk_stratification <- file.path(output_dir, "risk_stratification.png")
  
  # Dose response
  p4 <- plot_dose_response(population_results, 
                           save_path = file.path(output_dir, "dose_response.png"))
  saved_plots$dose_response <- file.path(output_dir, "dose_response.png")
  
  # Literature comparison
  p5 <- plot_literature_comparison(population_results, 
                                   save_path = file.path(output_dir, "literature_comparison.png"))
  saved_plots$literature_comparison <- file.path(output_dir, "literature_comparison.png")
  
  # CTCAE validation
  p6 <- plot_ctcae_validation(population_results, 
                              save_path = file.path(output_dir, "ctcae_validation.png"))
  saved_plots$ctcae_validation <- file.path(output_dir, "ctcae_validation.png")
  
  return(saved_plots)
}
