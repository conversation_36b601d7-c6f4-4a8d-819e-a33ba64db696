#!/usr/bin/env Rscript
#' QSP Thyroid Model - Diagnostic Check
#' ====================================
#'
#' This script performs comprehensive diagnostics to identify why
#' the QSP thyroid model scripts might not be running or producing output.
#'
#' Run this FIRST before attempting to run the main example_usage.R script.
#'
#' <AUTHOR> Modeling Team
#' @date 2024

cat("================================================================================\n")
cat("QSP THYROID MODEL - DIAGNOSTIC CHECK\n")
cat("================================================================================\n\n")

# =============================================================================
# SECTION 1: SYSTEM INFORMATION
# =============================================================================

cat("SECTION 1: SYSTEM INFORMATION\n")
cat(rep("-", 50), "\n", sep = "")

# R Version
cat("R Version:", R.version.string, "\n")
cat("Platform:", R.version$platform, "\n")
cat("Architecture:", R.version$arch, "\n")

# Working Directory
cat("Working Directory:", getwd(), "\n")

# Session Info
cat("Interactive Session:", interactive(), "\n")

# =============================================================================
# SECTION 2: FILE SYSTEM CHECK
# =============================================================================

cat("\nSECTION 2: FILE SYSTEM CHECK\n")
cat(rep("-", 50), "\n", sep = "")

# Check if required files exist
required_files <- c(
  "R/qsp_model_core.R",
  "R/qsp_population_analysis.R",
  "R/example_usage.R",
  "R/plotting_functions.R"
)

cat("Required Files:\n")
all_files_exist <- TRUE
for (file in required_files) {
  exists <- file.exists(file)
  status <- ifelse(exists, "✓ EXISTS", "✗ MISSING")
  cat(sprintf("  %-30s : %s\n", file, status))
  if (!exists) all_files_exist <- FALSE
}

# Check file permissions (if files exist)
cat("\nFile Permissions:\n")
for (file in required_files) {
  if (file.exists(file)) {
    info <- file.info(file)
    readable <- file.access(file, mode = 4) == 0
    cat(sprintf("  %-30s : %s\n", file, ifelse(readable, "✓ READABLE", "✗ NOT READABLE")))
  }
}

# =============================================================================
# SECTION 3: PACKAGE DEPENDENCIES
# =============================================================================

cat("\nSECTION 3: PACKAGE DEPENDENCIES\n")
cat(rep("-", 50), "\n", sep = "")

# Core required packages
core_packages <- c("R6", "deSolve", "dplyr", "futile.logger", "pracma")
additional_packages <- c("ggplot2", "readr", "purrr", "scales")
optional_packages <- c("pROC", "parallel")

check_packages <- function(packages, category) {
  cat(category, ":\n")
  all_installed <- TRUE
  for (pkg in packages) {
    installed <- requireNamespace(pkg, quietly = TRUE)
    status <- ifelse(installed, "✓ INSTALLED", "✗ MISSING")
    cat(sprintf("  %-15s : %s", pkg, status))
    
    if (installed) {
      # Get version if possible
      tryCatch({
        version <- as.character(packageVersion(pkg))
        cat(sprintf(" (v%s)", version))
      }, error = function(e) {})
    } else {
      all_installed <- FALSE
      cat(sprintf(" - Install with: install.packages('%s')", pkg))
    }
    cat("\n")
  }
  return(all_installed)
}

core_ok <- check_packages(core_packages, "Core Packages (REQUIRED)")
additional_ok <- check_packages(additional_packages, "\nAdditional Packages")
optional_ok <- check_packages(optional_packages, "\nOptional Packages")

# =============================================================================
# SECTION 4: BASIC FUNCTIONALITY TESTS
# =============================================================================

cat("\nSECTION 4: BASIC FUNCTIONALITY TESTS\n")
cat(rep("-", 50), "\n", sep = "")

# Test 1: R6 Class Creation
cat("Test 1: R6 Class Creation\n")
tryCatch({
  if (requireNamespace("R6", quietly = TRUE)) {
    TestClass <- R6::R6Class(
      "TestClass",
      public = list(
        x = 1,
        test_method = function() return("success")
      )
    )
    test_obj <- TestClass$new()
    result <- test_obj$test_method()
    cat("  Result: ✓ PASSED - R6 classes working correctly\n")
  } else {
    cat("  Result: ✗ FAILED - R6 package not available\n")
  }
}, error = function(e) {
  cat("  Result: ✗ FAILED - Error:", e$message, "\n")
})

# Test 2: ODE Solver
cat("\nTest 2: ODE Solver (deSolve)\n")
tryCatch({
  if (requireNamespace("deSolve", quietly = TRUE)) {
    # Simple exponential decay ODE: dy/dt = -y
    simple_ode <- function(t, y, parms) {
      list(c(-y[1]))
    }
    
    sol <- deSolve::ode(
      y = c(y = 1), 
      times = c(0, 1), 
      func = simple_ode, 
      parms = NULL,
      method = "lsoda"
    )
    
    final_value <- sol[nrow(sol), "y"]
    expected_value <- exp(-1)  # Analytical solution
    error <- abs(final_value - expected_value)
    
    if (error < 1e-6) {
      cat("  Result: ✓ PASSED - ODE solver working correctly\n")
      cat(sprintf("    Final value: %.6f (expected: %.6f)\n", final_value, expected_value))
    } else {
      cat("  Result: ⚠ WARNING - ODE solver may have accuracy issues\n")
      cat(sprintf("    Final value: %.6f (expected: %.6f, error: %.2e)\n", 
                  final_value, expected_value, error))
    }
  } else {
    cat("  Result: ✗ FAILED - deSolve package not available\n")
  }
}, error = function(e) {
  cat("  Result: ✗ FAILED - Error:", e$message, "\n")
})

# Test 3: Data Manipulation (dplyr)
cat("\nTest 3: Data Manipulation (dplyr)\n")
tryCatch({
  if (requireNamespace("dplyr", quietly = TRUE)) {
    test_data <- data.frame(x = 1:5, y = letters[1:5])
    result <- test_data %>% 
      dplyr::filter(x > 2) %>% 
      dplyr::summarise(n = dplyr::n())
    
    if (result$n == 3) {
      cat("  Result: ✓ PASSED - dplyr working correctly\n")
    } else {
      cat("  Result: ✗ FAILED - dplyr not working as expected\n")
    }
  } else {
    cat("  Result: ✗ FAILED - dplyr package not available\n")
  }
}, error = function(e) {
  cat("  Result: ✗ FAILED - Error:", e$message, "\n")
})

# Test 4: Numerical Integration (pracma)
cat("\nTest 4: Numerical Integration (pracma)\n")
tryCatch({
  if (requireNamespace("pracma", quietly = TRUE)) {
    x <- c(0, 1, 2, 3, 4)
    y <- c(0, 1, 4, 9, 16)  # y = x^2
    integral <- pracma::trapz(x, y)
    expected <- 64/3  # Analytical integral of x^2 from 0 to 4
    error <- abs(integral - expected)
    
    if (error < 1) {  # Reasonable tolerance for trapezoidal rule
      cat("  Result: ✓ PASSED - pracma integration working\n")
      cat(sprintf("    Computed: %.3f (expected: %.3f)\n", integral, expected))
    } else {
      cat("  Result: ⚠ WARNING - pracma integration may have issues\n")
    }
  } else {
    cat("  Result: ✗ FAILED - pracma package not available\n")
  }
}, error = function(e) {
  cat("  Result: ✗ FAILED - Error:", e$message, "\n")
})

# =============================================================================
# SECTION 5: QSP MODEL SPECIFIC TESTS
# =============================================================================

cat("\nSECTION 5: QSP MODEL SPECIFIC TESTS\n")
cat(rep("-", 50), "\n", sep = "")

# Test 5: Load QSP Core Model
cat("Test 5: Load QSP Core Model\n")
core_loaded <- FALSE
tryCatch({
  if (all_files_exist && file.exists("R/qsp_model_core.R")) {
    source("R/qsp_model_core.R")
    core_loaded <- TRUE
    cat("  Result: ✓ PASSED - QSP core model loaded successfully\n")
  } else {
    cat("  Result: ✗ FAILED - Required files missing\n")
  }
}, error = function(e) {
  cat("  Result: ✗ FAILED - Error loading core model:", e$message, "\n")
})

# Test 6: Create QSP Model Instance
cat("\nTest 6: Create QSP Model Instance\n")
model_created <- FALSE
if (core_loaded) {
  tryCatch({
    model <- QSPModel$new()
    model_created <- TRUE
    cat("  Result: ✓ PASSED - QSP model instance created\n")
    cat("  Model parameters initialized:", !is.null(model$params), "\n")
    cat("  Number of state variables:", model$n_states, "\n")
  }, error = function(e) {
    cat("  Result: ✗ FAILED - Error creating model:", e$message, "\n")
  })
} else {
  cat("  Result: ✗ SKIPPED - Core model not loaded\n")
}

# Test 7: Simple Simulation
cat("\nTest 7: Simple Simulation Test\n")
if (model_created) {
  tryCatch({
    # Very short simulation to test basic functionality
    result <- simulate_patient(
      model = model, 
      t_span = c(0, 7),  # 1 week only
      drug_type = 'nivolumab'
    )
    
    cat("  Result: ✓ PASSED - Basic simulation completed\n")
    cat("  Simulation points:", nrow(result), "\n")
    cat("  State variables:", ncol(result) - 3, "\n")  # Subtract time, drug_conc, derived vars
    cat("  Final TSH:", round(tail(result$TSH, 1), 3), "mIU/L\n")
    cat("  Final T3:", round(tail(result$T3, 1), 3), "pmol/L\n")
    cat("  Immune susceptible:", model$params$immune_susceptible, "\n")
    
  }, error = function(e) {
    cat("  Result: ✗ FAILED - Error in simulation:", e$message, "\n")
  })
} else {
  cat("  Result: ✗ SKIPPED - Model not created\n")
}

# =============================================================================
# SECTION 6: SUMMARY AND RECOMMENDATIONS
# =============================================================================

cat("\n", rep("=", 80), "\n", sep = "")
cat("DIAGNOSTIC SUMMARY\n")
cat(rep("=", 80), "\n", sep = "")

# Overall assessment
issues_found <- c()
if (!all_files_exist) issues_found <- c(issues_found, "Missing required files")
if (!core_ok) issues_found <- c(issues_found, "Missing core packages")
if (!core_loaded) issues_found <- c(issues_found, "Cannot load QSP model")
if (!model_created) issues_found <- c(issues_found, "Cannot create model instance")

if (length(issues_found) == 0) {
  cat("✓ OVERALL STATUS: READY TO RUN\n")
  cat("  All diagnostics passed. The QSP model should run successfully.\n\n")
  cat("NEXT STEPS:\n")
  cat("1. Run: source('R/example_usage.R')\n")
  cat("2. Or run: Rscript R/example_usage.R\n")
  cat("3. Check the 'example_outputs_R' directory for results\n")
} else {
  cat("⚠ OVERALL STATUS: ISSUES FOUND\n")
  cat("  The following issues need to be resolved:\n")
  for (issue in issues_found) {
    cat("  -", issue, "\n")
  }
  
  cat("\nRECOMMENDED ACTIONS:\n")
  if (!core_ok) {
    cat("1. Install missing packages:\n")
    missing_core <- core_packages[!sapply(core_packages, function(x) requireNamespace(x, quietly = TRUE))]
    if (length(missing_core) > 0) {
      cat("   install.packages(c(", paste0("'", missing_core, "'", collapse = ", "), "))\n")
    }
  }
  if (!all_files_exist) {
    cat("2. Ensure you're in the correct working directory\n")
    cat("3. Verify all R files are present and readable\n")
  }
  cat("4. Re-run this diagnostic after fixing issues\n")
}

cat("\nFor additional help, see: R_TROUBLESHOOTING_GUIDE.md\n")
cat(rep("=", 80), "\n", sep = "")
