import random
import numpy as np
from qsp_population_analysis import VirtualCohort

# Test the updated population simulation
print('Testing updated population simulation (n=10):')
print('='*60)

# Set fixed seed
random.seed(42)
np.random.seed(42)

cohort = VirtualCohort(n_patients=10)

print('Thyroid volume distribution:')
volumes = cohort.patients['thyroid_volume']
thyro_max_values = cohort.patients['Thyro_max']

print(f'  Min volume: {volumes.min():.1f} mL')
print(f'  Max volume: {volumes.max():.1f} mL')
print(f'  Mean volume: {volumes.mean():.1f} mL')

print(f'\nThyro_max distribution:')
print(f'  Min Thyro_max: {thyro_max_values.min():.3f}')
print(f'  Max Thyro_max: {thyro_max_values.max():.3f}')
print(f'  Mean Thyro_max: {thyro_max_values.mean():.3f}')

# Check Patient 4 specifically
patient_4 = cohort.patients.iloc[3]
print(f'\nPatient 4 (previously problematic):')
print(f'  Age: {patient_4["age"]:.1f} years')
print(f'  Thyroid volume: {patient_4["thyroid_volume"]:.1f} mL')
print(f'  Thyro_max: {patient_4["Thyro_max"]:.3f}')

# Run simulation
results = cohort.simulate_all()

print(f'\nSimulation results:')
susceptible_count = results['immune_susceptible'].sum()
print(f'Susceptible patients: {susceptible_count}/10 ({susceptible_count/10*100:.1f}%)')

overall_hypo_rate = results['any_hypothyroidism'].mean()
print(f'Overall hypothyroidism rate: {overall_hypo_rate*100:.1f}%')

# Check non-susceptible patients
non_susceptible_data = results[results['immune_susceptible'] == False]
if len(non_susceptible_data) > 0:
    non_susc_hypo_rate = non_susceptible_data['any_hypothyroidism'].mean()
    print(f'Non-susceptible hypothyroidism rate: {non_susc_hypo_rate*100:.1f}%')
    
    if non_susc_hypo_rate > 0:
        print('  WARNING: Non-susceptible patients still developing hypothyroidism!')
        hypo_non_susc = non_susceptible_data[non_susceptible_data['any_hypothyroidism'] == True]
        for i, row in hypo_non_susc.iterrows():
            print(f'    Patient {i+1}: {row["drug_type"]} | TSH: {row["peak_TSH_mIU_per_L"]:.2f} | Thyro_max: {row["Thyro_max"]:.3f}')
    else:
        print('  Good: No non-susceptible patients developing hypothyroidism')

print('\nDetailed patient breakdown:')
for i, row in results.iterrows():
    drug = row['drug_type']
    susceptible = row['immune_susceptible']
    hypo = row['any_hypothyroidism']
    tsh = row.get('peak_TSH_mIU_per_L', 0)
    thyro_max = row['Thyro_max']
    print(f'Patient {i+1:2d}: {drug:12s} | Susceptible: {str(susceptible):5s} | Hypo: {str(hypo):5s} | TSH: {tsh:5.2f} | Thyro_max: {thyro_max:.3f}')
