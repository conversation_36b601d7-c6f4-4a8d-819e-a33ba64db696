#!/usr/bin/env python3
"""
QSP Analysis Pipeline: Complete Workflow Execution
==================================================

This script runs the complete QSP analysis pipeline for checkpoint-inhibitor-induced
hypothyroidism, including:

1. Model parameter calibration (optional)
2. Virtual patient cohort generation
3. Population simulation
4. Risk stratification and analysis
5. Validation and reporting
6. Clinical decision support tool generation

Author: QSP Modeling Team
Date: 2024
License: MIT

Usage:
    python run_qsp_analysis.py --mode full --n_patients 1000
    python run_qsp_analysis.py --mode validation --external_data validation_cohort.csv
    python run_qsp_analysis.py --mode single_patient --patient_file patient_data.json
"""

import argparse
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# Import QSP modules
from qsp_model_core import QSPModel, ModelParameters, simulate_patient, calculate_risk_score
from qsp_calibration import GPCalibrator, CalibrationData, ObjectiveFunction
from qsp_population_analysis import VirtualCohort, RiskStratifier, PopulationParameters

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qsp_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QSPAnalysisPipeline:
    """
    Complete QSP analysis pipeline for checkpoint-inhibitor-induced hypothyroidism.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize analysis pipeline.
        
        Args:
            config: Configuration dictionary with analysis parameters
        """
        self.config = config
        self.results = {}
        
        # Create output directory
        self.output_dir = Path(config.get('output_dir', 'qsp_results'))
        self.output_dir.mkdir(exist_ok=True)
        
        logger.info(f"QSP Analysis Pipeline initialized")
        logger.info(f"Output directory: {self.output_dir}")
    
    def run_calibration(self, calibration_data: CalibrationData) -> Dict:
        """
        Run parameter calibration using Gaussian Process optimization.
        
        Args:
            calibration_data: Training dataset for calibration
            
        Returns:
            Dictionary with calibration results
        """
        logger.info("Starting parameter calibration...")
        
        try:
            calibrator = GPCalibrator(
                calibration_data=calibration_data,
                n_calls=self.config.get('calibration_n_calls', 50),
                random_state=self.config.get('random_state', 42)
            )
            
            best_params, best_objective = calibrator.optimize()
            
            calibration_results = {
                'best_parameters': best_params.tolist(),
                'best_objective': float(best_objective),
                'parameter_names': [dim.name for dim in calibrator.space],
                'n_calls': calibrator.n_calls
            }
            
            # Save calibration results
            with open(self.output_dir / 'calibration_results.json', 'w') as f:
                json.dump(calibration_results, f, indent=2)
            
            logger.info(f"Calibration completed. Best objective: {best_objective:.4f}")
            return calibration_results
            
        except Exception as e:
            logger.error(f"Calibration failed: {e}")
            return {}
    
    def run_population_simulation(self) -> pd.DataFrame:
        """
        Generate and simulate virtual patient cohort.
        
        Returns:
            DataFrame with simulation results
        """
        logger.info("Starting population simulation...")
        
        # Population parameters
        pop_params = PopulationParameters()
        
        # Override with config values if provided
        if 'population_params' in self.config:
            for key, value in self.config['population_params'].items():
                if hasattr(pop_params, key):
                    setattr(pop_params, key, value)
        
        # Generate virtual cohort
        cohort = VirtualCohort(
            n_patients=self.config.get('n_patients', 1000),
            pop_params=pop_params,
            random_state=self.config.get('random_state', 42)
        )
        
        # Simulate all patients
        results = cohort.simulate_all(
            t_span=self.config.get('simulation_time_span', (0, 168)),
            save_timeseries=self.config.get('save_timeseries', False)
        )
        
        # Save results
        results.to_csv(self.output_dir / 'population_simulation_results.csv', index=False)
        
        logger.info(f"Population simulation completed: {len(results)} patients")
        return results
    
    def run_risk_stratification(self, simulation_results: pd.DataFrame) -> Dict:
        """
        Perform risk stratification and analysis.
        
        Args:
            simulation_results: Results from population simulation
            
        Returns:
            Dictionary with stratification results
        """
        logger.info("Starting risk stratification...")
        
        stratifier = RiskStratifier(simulation_results)
        
        # Stratify patients
        risk_thresholds = self.config.get('risk_thresholds', [0.05, 0.15, 0.30])
        stratified_data = stratifier.stratify_patients(risk_thresholds)
        
        # Risk factor analysis
        risk_factors = stratifier.analyze_risk_factors()
        
        # Validation metrics
        validation_metrics = stratifier.validate_predictions()
        
        # Create visualizations
        stratifier.plot_risk_distribution(self.output_dir / 'risk_distribution.png')
        
        # Save results
        stratified_data.to_csv(self.output_dir / 'risk_stratified_results.csv', index=False)
        risk_factors.to_csv(self.output_dir / 'risk_factor_analysis.csv', index=False)
        
        with open(self.output_dir / 'validation_metrics.json', 'w') as f:
            json.dump(validation_metrics, f, indent=2)
        
        stratification_results = {
            'risk_thresholds': risk_thresholds,
            'risk_factor_analysis': risk_factors.to_dict('records'),
            'validation_metrics': validation_metrics,
            'n_patients': int(len(stratified_data)),
            'overall_incidence': float(stratified_data['grade2_hypothyroidism'].mean()),
            'n_events': int(validation_metrics['n_events'])
        }
        
        logger.info("Risk stratification completed")
        return stratification_results
    
    def run_external_validation(self, external_data: pd.DataFrame) -> Dict:
        """
        Validate model against external dataset.
        
        Args:
            external_data: External validation dataset
            
        Returns:
            Dictionary with validation results
        """
        logger.info("Starting external validation...")
        
        # Simulate external cohort using same model
        validation_results = []
        
        for i, patient in external_data.iterrows():
            try:
                # Create patient-specific parameters
                params = ModelParameters()
                
                # Map external data to model parameters
                if 'sex' in patient:
                    params.sex_factor = 1.3 if patient['sex'] == 'F' else 1.0
                if 'age' in patient:
                    params.age_factor = 1.2 if patient['age'] > 60 else 1.0
                if 'TPO_Ab' in patient:
                    params.TPO_Ab_titer = np.log10(max(patient['TPO_Ab'], 1))
                if 'HLA_DRB1_03' in patient:
                    params.HLA_factor = 2.2 if patient['HLA_DRB1_03'] else 1.0
                
                # Simulate patient
                model = QSPModel(params)
                drug_type = patient.get('drug_type', 'nivolumab')
                sim_result = simulate_patient(model, drug_type=drug_type)
                
                # Calculate predictions
                risk_metrics = calculate_risk_score(model, sim_result)
                
                # Add patient ID and observed outcomes
                risk_metrics['patient_id'] = patient.get('patient_id', f'EXT_{i}')
                risk_metrics['observed_hypothyroidism'] = patient.get('hypothyroidism', 0)
                risk_metrics['observed_onset_time'] = patient.get('onset_time', np.nan)
                
                validation_results.append(risk_metrics)
                
            except Exception as e:
                logger.warning(f"External validation failed for patient {i}: {e}")
                continue
        
        validation_df = pd.DataFrame(validation_results)
        
        # Compute validation metrics
        if len(validation_df) > 0:
            stratifier = RiskStratifier(validation_df)
            validation_metrics = stratifier.validate_predictions()
            
            # Additional external validation metrics
            if 'observed_hypothyroidism' in validation_df.columns:
                from sklearn.metrics import roc_auc_score, brier_score_loss
                
                y_true = validation_df['observed_hypothyroidism']
                y_pred = validation_df['grade2_hypothyroidism']
                
                external_auc = roc_auc_score(y_true, y_pred)
                external_brier = brier_score_loss(y_true, y_pred)
                
                validation_metrics.update({
                    'external_auc': external_auc,
                    'external_brier': external_brier
                })
        else:
            validation_metrics = {}
        
        # Save results
        validation_df.to_csv(self.output_dir / 'external_validation_results.csv', index=False)
        
        with open(self.output_dir / 'external_validation_metrics.json', 'w') as f:
            json.dump(validation_metrics, f, indent=2)
        
        logger.info(f"External validation completed: {len(validation_df)} patients")
        return validation_metrics
    
    def simulate_single_patient(self, patient_data: Dict) -> Dict:
        """
        Simulate single patient for clinical decision support.
        
        Args:
            patient_data: Dictionary with patient characteristics
            
        Returns:
            Dictionary with risk assessment and recommendations
        """
        logger.info("Simulating single patient...")
        
        # Create patient-specific parameters
        params = ModelParameters()
        
        # Map patient data to model parameters
        params.sex_factor = 1.3 if patient_data.get('sex') == 'F' else 1.0
        params.age_factor = 1.2 if patient_data.get('age', 0) > 60 else 1.0
        params.TPO_Ab_titer = np.log10(max(patient_data.get('TPO_Ab', 1), 1))
        params.HLA_factor = 2.2 if patient_data.get('HLA_DRB1_03', False) else 1.0
        
        if 'thyroid_volume' in patient_data:
            params.Thyro_max = patient_data['thyroid_volume'] / 18.0  # Normalize to average
        
        # Create model and simulate
        model = QSPModel(params)
        drug_type = patient_data.get('drug_type', 'nivolumab')
        sim_result = simulate_patient(model, drug_type=drug_type)
        
        # Calculate risk metrics
        risk_metrics = calculate_risk_score(model, sim_result)
        
        # Generate clinical recommendations
        recommendations = self._generate_recommendations(risk_metrics, patient_data)
        
        # Combine results
        patient_assessment = {
            'patient_data': patient_data,
            'risk_metrics': risk_metrics,
            'recommendations': recommendations,
            'simulation_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save results
        with open(self.output_dir / 'single_patient_assessment.json', 'w') as f:
            json.dump(patient_assessment, f, indent=2)
        
        logger.info("Single patient simulation completed")
        return patient_assessment
    
    def _generate_recommendations(self, risk_metrics: Dict, patient_data: Dict) -> Dict:
        """Generate clinical recommendations based on risk assessment."""
        
        risk_score = risk_metrics['grade2_hypothyroidism']
        
        recommendations = {
            'risk_category': 'Low',
            'monitoring_frequency': 'Standard (every 6 weeks)',
            'interventions': [],
            'patient_education': 'Standard thyroid symptom awareness'
        }
        
        if risk_score > 0.30:
            recommendations.update({
                'risk_category': 'Very High',
                'monitoring_frequency': 'Intensive (every 2 weeks for 12 weeks)',
                'interventions': [
                    'Consider pre-emptive corticosteroids if IFN-γ >250 pg/mL',
                    'Consider PD-L1 inhibitor if therapeutic equipoise exists',
                    'Endocrinology consultation recommended'
                ],
                'patient_education': 'Enhanced symptom monitoring, early contact instructions'
            })
        elif risk_score > 0.15:
            recommendations.update({
                'risk_category': 'High',
                'monitoring_frequency': 'Enhanced (every 3 weeks for 12 weeks)',
                'interventions': [
                    'Consider PD-L1 inhibitor if therapeutic equipoise exists',
                    'Alert threshold: TSH >5 mIU/L'
                ],
                'patient_education': 'Enhanced symptom monitoring'
            })
        elif risk_score > 0.05:
            recommendations.update({
                'risk_category': 'Moderate',
                'monitoring_frequency': 'Standard with patient education',
                'interventions': ['Alert threshold: TSH >10 mIU/L'],
                'patient_education': 'Standard thyroid symptom awareness'
            })
        
        return recommendations
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report."""
        
        report_lines = [
            "QSP Analysis Report: Checkpoint-Inhibitor-Induced Hypothyroidism",
            "=" * 70,
            "",
            f"Analysis Date: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"Configuration: {self.config.get('mode', 'unknown')}",
            ""
        ]
        
        # Add results sections
        if 'population_results' in self.results:
            pop_results = self.results['population_results']
            report_lines.extend([
                "Population Simulation Results:",
                f"- Total patients simulated: {len(pop_results)}",
                f"- Overall incidence (Grade >=2): {pop_results['grade2_hypothyroidism'].mean():.1%}",
                f"- Median time to onset: {pop_results[pop_results['grade2_hypothyroidism']==1]['time_to_onset_days'].median():.1f} days",
                ""
            ])
        
        if 'stratification_results' in self.results:
            strat_results = self.results['stratification_results']
            report_lines.extend([
                "Risk Stratification Results:",
                f"- Overall incidence: {strat_results['overall_incidence']:.1%}",
                f"- Risk thresholds: {strat_results['risk_thresholds']}",
                ""
            ])
            
            if 'validation_metrics' in strat_results:
                vm = strat_results['validation_metrics']
                report_lines.extend([
                    "Validation Metrics:",
                    f"- AUC-ROC: {vm.get('auc_roc', 'N/A'):.3f}",
                    f"- Brier Score: {vm.get('brier_score', 'N/A'):.3f}",
                    f"- Calibration Slope: {vm.get('calibration_slope', 'N/A'):.3f}",
                    ""
                ])
        
        report_text = "\n".join(report_lines)
        
        # Save report
        with open(self.output_dir / 'analysis_report.txt', 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        return report_text

def main():
    """Main execution function."""
    
    parser = argparse.ArgumentParser(description='QSP Analysis Pipeline')
    parser.add_argument('--mode', choices=['full', 'validation', 'single_patient', 'calibration'],
                       default='full', help='Analysis mode')
    parser.add_argument('--n_patients', type=int, default=1000,
                       help='Number of virtual patients to simulate')
    parser.add_argument('--external_data', type=str,
                       help='Path to external validation dataset')
    parser.add_argument('--patient_file', type=str,
                       help='Path to single patient data file (JSON)')
    parser.add_argument('--config', type=str,
                       help='Path to configuration file (JSON)')
    parser.add_argument('--output_dir', type=str, default='qsp_results',
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {}
    
    # Override config with command line arguments
    config.update({
        'mode': args.mode,
        'n_patients': args.n_patients,
        'output_dir': args.output_dir
    })
    
    # Initialize pipeline
    pipeline = QSPAnalysisPipeline(config)
    
    try:
        if args.mode == 'full':
            # Full analysis pipeline
            logger.info("Running full analysis pipeline...")
            
            # Population simulation
            pop_results = pipeline.run_population_simulation()
            pipeline.results['population_results'] = pop_results
            
            # Risk stratification
            strat_results = pipeline.run_risk_stratification(pop_results)
            pipeline.results['stratification_results'] = strat_results
            
        elif args.mode == 'validation':
            # External validation
            if not args.external_data:
                raise ValueError("External data file required for validation mode")
            
            external_data = pd.read_csv(args.external_data)
            validation_results = pipeline.run_external_validation(external_data)
            pipeline.results['validation_results'] = validation_results
            
        elif args.mode == 'single_patient':
            # Single patient simulation
            if not args.patient_file:
                raise ValueError("Patient data file required for single patient mode")
            
            with open(args.patient_file, 'r') as f:
                patient_data = json.load(f)
            
            patient_results = pipeline.simulate_single_patient(patient_data)
            pipeline.results['patient_results'] = patient_results
            
        # Generate report
        report = pipeline.generate_report()
        print(report)
        
        logger.info(f"Analysis completed successfully. Results saved to {pipeline.output_dir}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
