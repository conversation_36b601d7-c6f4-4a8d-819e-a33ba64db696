from qsp_population_analysis import VirtualCohort
import pandas as pd

# Test with a small cohort to debug the issue
print('Testing small population (n=10):')
print('='*50)

cohort = VirtualCohort(n_patients=10)
results = cohort.simulate_all()

print('Available columns:', list(results.columns))
print()

# Check immune susceptibility distribution
susceptible_count = results['immune_susceptible'].sum()
print(f'Susceptible patients: {susceptible_count}/10 ({susceptible_count/10*100:.1f}%)')

print()
print('Individual patient breakdown:')
for i, row in results.iterrows():
    drug = row['drug_type']
    susceptible = row['immune_susceptible']
    susc_level = row.get('susceptibility_level', 'unknown')
    hypo = row['any_hypothyroidism']
    tsh = row.get('peak_TSH_mIU_per_L', 0)
    print(f'Patient {i+1:2d}: {drug:12s} | Susceptible: {str(susceptible):5s} ({susc_level:4s}) | Hypo: {str(hypo):5s} | TSH: {tsh:5.2f}')

print()
print('Summary by susceptibility:')
susceptible_data = results[results['immune_susceptible'] == True]
non_susceptible_data = results[results['immune_susceptible'] == False]

if len(susceptible_data) > 0:
    susc_hypo_rate = susceptible_data['any_hypothyroidism'].mean()
    print(f'Susceptible patients: {len(susceptible_data)} total, {susc_hypo_rate*100:.1f}% hypothyroidism rate')
    print('  Susceptible patient details:')
    for i, row in susceptible_data.iterrows():
        print(f'    Patient {i+1}: {row["drug_type"]} | {row["susceptibility_level"]} | TSH: {row["peak_TSH_mIU_per_L"]:.2f}')

if len(non_susceptible_data) > 0:
    non_susc_hypo_rate = non_susceptible_data['any_hypothyroidism'].mean()
    print(f'Non-susceptible patients: {len(non_susceptible_data)} total, {non_susc_hypo_rate*100:.1f}% hypothyroidism rate')
    if non_susc_hypo_rate > 0:
        print('  WARNING: Non-susceptible patients developing hypothyroidism!')
        hypo_non_susc = non_susceptible_data[non_susceptible_data['any_hypothyroidism'] == True]
        for i, row in hypo_non_susc.iterrows():
            print(f'    Patient {i+1}: {row["drug_type"]} | TSH: {row["peak_TSH_mIU_per_L"]:.2f}')

print()
print('Overall incidence rates:')
overall_hypo_rate = results['any_hypothyroidism'].mean()
print(f'Overall hypothyroidism rate: {overall_hypo_rate*100:.1f}%')

print()
print('Drug-specific incidence rates:')
for drug in results['drug_type'].unique():
    drug_data = results[results['drug_type'] == drug]
    drug_hypo_rate = drug_data['any_hypothyroidism'].mean()
    drug_count = len(drug_data)
    print(f'{drug}: {drug_hypo_rate*100:.1f}% ({drug_count} patients)')
