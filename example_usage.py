#!/usr/bin/env python3
"""
Example usage of the QSP thyroid model with final optimized parameters.
This script demonstrates the improved model with realistic timing of months instead of days.
Tests with higher susceptibility patients and analyzes drug-specific results.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from qsp_model_core import QSPModel, simulate_patient, calculate_risk_score, run_population_simulation

# Set plotting style for publication-quality figures
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 12,
    'axes.labelsize': 14,
    'axes.titlesize': 16,
    'xtick.labelsize': 12,
    'ytick.labelsize': 12,
    'legend.fontsize': 12,
    'figure.dpi': 100,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

def validate_against_literature(results_df: pd.DataFrame) -> dict:
    """
    Validate model results against clinical literature data.
    
    Args:
        results_df: Population simulation results
        
    Returns:
        Dictionary with validation metrics
    """
    validation = {}
    
    # Literature-reported incidence rates
    literature_rates = {
        'nivolumab_any': (5.0, 15.0),  # 5-15% any hypothyroidism
        'nivolumab_grade2': (2.0, 8.0),  # 2-8% grade 2+ hypothyroidism
        'pembrolizumab_any': (6.0, 18.0),  # 6-18% any hypothyroidism
        'pembrolizumab_grade2': (3.0, 10.0),  # 3-10% grade 2+ hypothyroidism
        'time_to_onset_range': (30, 180),  # 1-6 months typical range
        'time_to_onset_median': (60, 120)  # 2-4 months median
    }
    
    # Calculate model incidence rates
    total_patients = len(results_df)
    any_hypo_count = results_df['any_hypothyroidism'].sum()
    grade2_hypo_count = results_df['grade2_hypothyroidism'].sum()
    
    model_any_rate = (any_hypo_count / total_patients) * 100
    model_grade2_rate = (grade2_hypo_count / total_patients) * 100
    
    # Time to onset analysis
    hypo_patients = results_df[results_df['any_hypothyroidism'] == 1.0]
    if len(hypo_patients) > 0:
        time_to_onset_days = hypo_patients['time_to_onset_days'].dropna()
        if len(time_to_onset_days) > 0:
            model_median_onset = time_to_onset_days.median()
            model_mean_onset = time_to_onset_days.mean()
            model_min_onset = time_to_onset_days.min()
            model_max_onset = time_to_onset_days.max()
        else:
            model_median_onset = model_mean_onset = model_min_onset = model_max_onset = np.nan
    else:
        model_median_onset = model_mean_onset = model_min_onset = model_max_onset = np.nan
    
    # Validation checks
    validation['incidence_rates'] = {
        'model_any_rate': model_any_rate,
        'model_grade2_rate': model_grade2_rate,
        'literature_any_range': literature_rates['nivolumab_any'],
        'literature_grade2_range': literature_rates['nivolumab_grade2'],
        'any_rate_within_range': (literature_rates['nivolumab_any'][0] <= model_any_rate <= literature_rates['nivolumab_any'][1]),
        'grade2_rate_within_range': (literature_rates['nivolumab_grade2'][0] <= model_grade2_rate <= literature_rates['nivolumab_grade2'][1])
    }
    
    validation['time_to_onset'] = {
        'model_median_days': model_median_onset,
        'model_median_months': model_median_onset / 30 if not np.isnan(model_median_onset) else np.nan,
        'model_mean_days': model_mean_onset,
        'model_mean_months': model_mean_onset / 30 if not np.isnan(model_mean_onset) else np.nan,
        'model_range_days': (model_min_onset, model_max_onset),
        'model_range_months': (model_min_onset / 30, model_max_onset / 30) if not np.isnan(model_min_onset) else (np.nan, np.nan),
        'literature_range_days': literature_rates['time_to_onset_range'],
        'literature_median_range_days': literature_rates['time_to_onset_median'],
        'onset_within_range': (literature_rates['time_to_onset_range'][0] <= model_median_onset <= literature_rates['time_to_onset_range'][1]) if not np.isnan(model_median_onset) else False
    }
    
    return validation

def plot_population_results(results_df: pd.DataFrame, save_dir: str = "example_outputs"):
    """
    Generate comprehensive plots for population simulation results.
    
    Args:
        results_df: Population simulation results DataFrame
        save_dir: Directory to save plots
    """
    # Create output directory
    Path(save_dir).mkdir(exist_ok=True)
    
    # 1. Incidence Rate Comparison Plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Calculate incidence rates by drug
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    any_rates = []
    grade2_rates = []
    
    for drug in drugs:
        drug_data = results_df[results_df['drug_type'] == drug]
        if len(drug_data) > 0:
            any_rate = (drug_data['any_hypothyroidism'].sum() / len(drug_data)) * 100
            grade2_rate = (drug_data['grade2_hypothyroidism'].sum() / len(drug_data)) * 100
        else:
            any_rate = 0.0
            grade2_rate = 0.0
        any_rates.append(any_rate)
        grade2_rates.append(grade2_rate)
    
    # Plot incidence rates
    x_pos = np.arange(len(drugs))
    width = 0.35
    
    bars1 = ax1.bar(x_pos - width/2, any_rates, width, label='Any Hypothyroidism', alpha=0.8, color='skyblue')
    bars2 = ax1.bar(x_pos + width/2, grade2_rates, width, label='Grade 2+ Hypothyroidism', alpha=0.8, color='orange')
    
    # Add literature reference ranges
    ax1.axhspan(5, 15, alpha=0.2, color='skyblue', label='Literature Range (Any)')
    ax1.axhspan(2, 8, alpha=0.2, color='orange', label='Literature Range (Grade 2+)')
    
    ax1.set_xlabel('Checkpoint Inhibitor')
    ax1.set_ylabel('Incidence Rate (%)')
    ax1.set_title('Hypothyroidism Incidence Rates by Drug')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels([drug.capitalize() for drug in drugs], rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar in bars1:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.1f}%', ha='center', va='bottom')
    for bar in bars2:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{height:.1f}%', ha='center', va='bottom')
    
    # 2. Time to Onset Distribution
    hypo_patients = results_df[results_df['any_hypothyroidism'] == 1.0]
    if len(hypo_patients) > 0:
        onset_times = hypo_patients['time_to_onset_days'].dropna()
        onset_months = onset_times / 30.44  # Convert to months
        
        ax2.hist(onset_months, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        ax2.axvline(onset_months.mean(), color='red', linestyle='--', linewidth=2, 
                   label=f'Mean: {onset_months.mean():.1f} months')
        ax2.axvline(onset_months.median(), color='darkred', linestyle='--', linewidth=2,
                   label=f'Median: {onset_months.median():.1f} months')
        
        # Add literature reference range
        ax2.axvspan(1, 6, alpha=0.2, color='green', label='Literature Range (1-6 months)')
        
        ax2.set_xlabel('Time to Onset (months)')
        ax2.set_ylabel('Number of Patients')
        ax2.set_title('Distribution of Time to Onset')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/population_incidence_and_timing.png')
    plt.close()
    
    # 3. Drug-Specific Time to Onset Comparison
    fig, ax = plt.subplots(figsize=(14, 8))
    
    onset_data = []
    drug_labels = []
    
    for drug in drugs:
        drug_hypo = results_df[(results_df['drug_type'] == drug) & 
                              (results_df['any_hypothyroidism'] == 1.0)]
        if len(drug_hypo) > 0:
            onset_times = drug_hypo['time_to_onset_days'].dropna() / 30.44  # Convert to months
            onset_data.append(onset_times)
            drug_labels.append(drug.capitalize())
    
    if onset_data:
        # Create violin plot
        parts = ax.violinplot(onset_data, positions=range(len(drug_labels)), showmeans=True, showmedians=True)
        
        # Customize violin plot
        for pc in parts['bodies']:
            pc.set_facecolor('lightblue')
            pc.set_alpha(0.7)
        
        ax.set_xlabel('Checkpoint Inhibitor')
        ax.set_ylabel('Time to Onset (months)')
        ax.set_title('Time to Onset Distribution by Drug')
        ax.set_xticks(range(len(drug_labels)))
        ax.set_xticklabels(drug_labels, rotation=45)
        
        # Add literature reference range
        ax.axhspan(1, 6, alpha=0.2, color='green', label='Literature Range (1-6 months)')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/drug_specific_onset_timing.png')
    plt.close()
    
    # 4. Model Validation Summary Plot
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Calculate overall metrics
    total_patients = len(results_df)
    any_hypo_rate = (results_df['any_hypothyroidism'].sum() / total_patients) * 100
    grade2_hypo_rate = (results_df['grade2_hypothyroidism'].sum() / total_patients) * 100
    
    if len(hypo_patients) > 0:
        median_onset_months = hypo_patients['time_to_onset_days'].median() / 30.44
    else:
        median_onset_months = 0
    
    # Create validation comparison
    metrics = ['Any Hypothyroidism\n(%)', 'Grade 2+ Hypothyroidism\n(%)', 'Median Time to Onset\n(months)']
    model_values = [any_hypo_rate, grade2_hypo_rate, median_onset_months]
    lit_min = [5, 2, 1]
    lit_max = [15, 8, 6]
    
    x_pos = np.arange(len(metrics))
    
    # Plot model values
    bars = ax.bar(x_pos, model_values, alpha=0.8, color=['skyblue', 'orange', 'lightcoral'])
    
    # Add literature ranges
    for i, (min_val, max_val) in enumerate(zip(lit_min, lit_max)):
        ax.errorbar(i, (min_val + max_val) / 2, yerr=(max_val - min_val) / 2,
                   fmt='o', color='red', markersize=8, capsize=10, capthick=3, 
                   label='Literature Range' if i == 0 else "")
    
    ax.set_xlabel('Validation Metrics')
    ax.set_ylabel('Values')
    ax.set_title('Model Validation Against Literature')
    ax.set_xticks(x_pos)
    ax.set_xticklabels(metrics)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (bar, value) in enumerate(zip(bars, model_values)):
        height = bar.get_height()
        if i < 2:  # Percentage values
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                   f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
        else:  # Time value
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/model_validation_summary.png')
    plt.close()
    
    print(f"\n📊 Comprehensive plots saved to '{save_dir}/' directory:")
    print(f"  - population_incidence_and_timing.png")
    print(f"  - drug_specific_onset_timing.png") 
    print(f"  - model_validation_summary.png")

def analyze_drug_specific_results():
    """Analyze drug-specific time to onset results."""
    
    print("\n6. Drug-Specific Time to Onset Analysis")
    print("-" * 60)
    
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    drug_results = {}
    
    for drug in drugs:
        print(f"\n  Testing {drug.capitalize()}...")
        
        # Create population with higher susceptibility focus
        n_patients = 1000
        patient_ids = [f"VP_{drug}_{i:04d}" for i in range(1, n_patients + 1)]
        
        # Generate higher susceptibility values (focus on > 1.8)
        np.random.seed(42)  # For reproducible results
        base_susceptibilities = np.random.uniform(0.5, 2.0, n_patients)
        # Boost some patients to higher susceptibility
        high_susceptibility_mask = np.random.choice([True, False], n_patients, p=[0.3, 0.7])
        damage_susceptibilities = np.where(high_susceptibility_mask, 
                                         np.random.uniform(1.8, 2.0, n_patients),
                                         base_susceptibilities)
        
        # Run population simulation for this drug
        population_results = run_population_simulation(
            patient_ids=patient_ids,
            damage_susceptibilities=damage_susceptibilities,
            t_span=(0, 730),  # 24 months
            drug_type=drug
        )
        
        # Calculate drug-specific statistics
        total_patients = len(population_results)
        any_hypo_count = population_results['any_hypothyroidism'].sum()
        grade2_hypo_count = population_results['grade2_hypothyroidism'].sum()
        
        any_rate = (any_hypo_count / total_patients) * 100
        grade2_rate = (grade2_hypo_count / total_patients) * 100
        
        # Time to onset analysis
        hypo_patients = population_results[population_results['any_hypothyroidism'] == 1.0]
        if len(hypo_patients) > 0:
            time_to_onset_days = hypo_patients['time_to_onset_days'].dropna()
            if len(time_to_onset_days) > 0:
                median_onset = time_to_onset_days.median()
                mean_onset = time_to_onset_days.mean()
                min_onset = time_to_onset_days.min()
                max_onset = time_to_onset_days.max()
            else:
                median_onset = mean_onset = min_onset = max_onset = np.nan
        else:
            median_onset = mean_onset = min_onset = max_onset = np.nan
        
        # Store results
        drug_results[drug] = {
            'any_rate': any_rate,
            'grade2_rate': grade2_rate,
            'median_onset_days': median_onset,
            'median_onset_months': median_onset / 30 if not np.isnan(median_onset) else np.nan,
            'mean_onset_days': mean_onset,
            'mean_onset_months': mean_onset / 30 if not np.isnan(mean_onset) else np.nan,
            'range_onset_days': (min_onset, max_onset),
            'range_onset_months': (min_onset / 30, max_onset / 30) if not np.isnan(min_onset) else (np.nan, np.nan),
            'n_hypo_patients': len(hypo_patients)
        }
        
        # Print results
        print(f"    - Any hypothyroidism: {any_rate:.1f}% ({any_hypo_count}/{total_patients})")
        print(f"    - Grade 2+ hypothyroidism: {grade2_rate:.1f}% ({grade2_hypo_count}/{total_patients})")
        
        if not np.isnan(median_onset):
            print(f"    - Median time to onset: {median_onset:.1f} days ({median_onset/30:.1f} months)")
            print(f"    - Mean time to onset: {mean_onset:.1f} days ({mean_onset/30:.1f} months)")
            print(f"    - Range: {min_onset:.1f} - {max_onset:.1f} days ({min_onset/30:.1f} - {max_onset/30:.1f} months)")
        else:
            print(f"    - No patients developed hypothyroidism")
    
    return drug_results

def main():
    """Main function to demonstrate the optimized QSP thyroid model."""
    
    print("=" * 80)
    print("QSP THYROID MODEL - FINAL PARAMETER OPTIMIZATION")
    print("=" * 80)
    
    # Test single patient with different damage susceptibility levels
    print("\n1. Testing Patient-Specific Damage Susceptibility")
    print("-" * 60)
    
    susceptibility_levels = [0.5, 1.0, 1.5, 2.0]  # Low, Normal, High, Very High
    
    for sus in susceptibility_levels:
        model = QSPModel(patient_id=f"VP_TEST_{sus}", damage_susceptibility=sus)
        
        # Simulate for 24 months (730 days) for longer-term analysis
        results = simulate_patient(model, t_span=(0, 730), drug_type='nivolumab')
        
        # Calculate risk metrics
        risk = calculate_risk_score(model, results, time_horizon=730)
        
        print(f"  Damage Susceptibility {sus}:")
        print(f"    - Any hypothyroidism: {'Yes' if risk['any_hypothyroidism'] else 'No'}")
        print(f"    - Grade 2+ hypothyroidism: {'Yes' if risk['grade2_hypothyroidism'] else 'No'}")
        if risk['any_hypothyroidism']:
            print(f"    - Time to onset: {risk['time_to_onset_days']:.1f} days ({risk['time_to_onset_days']/30:.1f} months)")
        print()
    
    # Test population simulation with literature-matched parameters
    print("\n2. Population Simulation with Literature-Matched Parameters (24 months, 1000 patients)")
    print("-" * 80)
    
    # Create population with 250 patients per drug (4 drugs = 1000 total)
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    n_patients_per_drug = 250
    total_patients = len(drugs) * n_patients_per_drug
    
    print(f"  Running simulation for {len(drugs)} drugs with {n_patients_per_drug} patients each ({total_patients} total)")
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Collect all results
    all_population_results = []
    
    for drug_idx, drug in enumerate(drugs):
        print(f"\n  📊 Simulating {drug.upper()} (Drug {drug_idx + 1}/{len(drugs)})...")
        
        # Create patient IDs for this drug
        start_id = drug_idx * n_patients_per_drug + 1
        end_id = (drug_idx + 1) * n_patients_per_drug + 1
        patient_ids = [f"VP{i:04d}" for i in range(start_id, end_id)]
        
        # Generate susceptibility values for this drug cohort
        base_susceptibilities = np.random.uniform(0.5, 2.0, n_patients_per_drug)
        # Boost some patients to higher susceptibility (30% of patients)
        high_susceptibility_mask = np.random.choice([True, False], n_patients_per_drug, p=[0.3, 0.7])
        damage_susceptibilities = np.where(high_susceptibility_mask, 
                                         np.random.uniform(1.8, 2.0, n_patients_per_drug),
                                         base_susceptibilities)
        
        # Run simulation for this drug
        drug_results = run_population_simulation(
            patient_ids=patient_ids,
            damage_susceptibilities=damage_susceptibilities,
            t_span=(0, 730),  # 24 months
            drug_type=drug
        )
        
        all_population_results.append(drug_results)
        
        # Print interim results for this drug
        drug_any_hypo = drug_results['any_hypothyroidism'].sum()
        drug_grade2_hypo = drug_results['grade2_hypothyroidism'].sum()
        print(f"    - Any hypothyroidism: {drug_any_hypo}/{n_patients_per_drug} ({drug_any_hypo/n_patients_per_drug*100:.1f}%)")
        print(f"    - Grade 2+ hypothyroidism: {drug_grade2_hypo}/{n_patients_per_drug} ({drug_grade2_hypo/n_patients_per_drug*100:.1f}%)")
        
        if drug_any_hypo > 0:
            drug_hypo_patients = drug_results[drug_results['any_hypothyroidism'] == 1.0]
            drug_median_onset = drug_hypo_patients['time_to_onset_days'].median()
            print(f"    - Median time to onset: {drug_median_onset:.0f} days ({drug_median_onset/30.44:.1f} months)")
    
    # Combine all drug results
    population_results = pd.concat(all_population_results, ignore_index=True)
    print(f"\n  ✅ Combined results: {len(population_results)} patients across {len(drugs)} drugs")
    
    # Calculate population statistics
    total_patients = len(population_results)
    any_hypo_count = population_results['any_hypothyroidism'].sum()
    grade2_hypo_count = population_results['grade2_hypothyroidism'].sum()
    
    print(f"  Population Results ({total_patients} patients, 24 months):")
    print(f"    - Any hypothyroidism: {any_hypo_count} ({any_hypo_count/total_patients*100:.1f}%)")
    print(f"    - Grade 2+ hypothyroidism: {grade2_hypo_count} ({grade2_hypo_count/total_patients*100:.1f}%)")
    
    # Analyze time to onset for patients who developed hypothyroidism
    hypo_patients = population_results[population_results['any_hypothyroidism'] == 1.0]
    if len(hypo_patients) > 0:
        mean_onset = hypo_patients['time_to_onset_days'].mean()
        median_onset = hypo_patients['time_to_onset_days'].median()
        min_onset = hypo_patients['time_to_onset_days'].min()
        max_onset = hypo_patients['time_to_onset_days'].max()
        
        print(f"    - Time to onset (days): Mean={mean_onset:.1f}, Median={median_onset:.1f}")
        print(f"    - Time to onset range: {min_onset:.1f} - {max_onset:.1f} days")
        print(f"    - Time to onset (months): Mean={mean_onset/30:.1f}, Median={median_onset/30:.1f}")
        print(f"    - Time to onset range (months): {min_onset/30:.1f} - {max_onset/30:.1f}")
    
    # Validate against clinical literature
    print("\n3. Literature Validation")
    print("-" * 60)
    
    validation = validate_against_literature(population_results)
    
    print("  Incidence Rate Validation:")
    print(f"    - Model any hypothyroidism: {validation['incidence_rates']['model_any_rate']:.1f}%")
    print(f"    - Literature range: {validation['incidence_rates']['literature_any_range'][0]}-{validation['incidence_rates']['literature_any_range'][1]}%")
    print(f"    - Within literature range: {'✅ YES' if validation['incidence_rates']['any_rate_within_range'] else '❌ NO'}")
    
    print(f"    - Model grade 2+ hypothyroidism: {validation['incidence_rates']['model_grade2_rate']:.1f}%")
    print(f"    - Literature range: {validation['incidence_rates']['literature_grade2_range'][0]}-{validation['incidence_rates']['literature_grade2_range'][1]}%")
    print(f"    - Within literature range: {'✅ YES' if validation['incidence_rates']['grade2_rate_within_range'] else '❌ NO'}")
    
    print("\n  Time to Onset Validation:")
    if not np.isnan(validation['time_to_onset']['model_median_days']):
        print(f"    - Model median onset: {validation['time_to_onset']['model_median_days']:.1f} days ({validation['time_to_onset']['model_median_months']:.1f} months)")
        print(f"    - Literature range: {validation['time_to_onset']['literature_range_days'][0]}-{validation['time_to_onset']['literature_range_days'][1]} days")
        print(f"    - Within literature range: {'✅ YES' if validation['time_to_onset']['onset_within_range'] else '❌ NO'}")
    else:
        print("    - No patients developed hypothyroidism in model")
    
    # Analyze high susceptibility patients
    print("\n4. High Susceptibility Patient Analysis")
    print("-" * 60)
    
    high_susceptibility_patients = population_results[population_results['damage_susceptibility'] > 1.8]
    if len(high_susceptibility_patients) > 0:
        high_sus_hypo = high_susceptibility_patients[high_susceptibility_patients['any_hypothyroidism'] == 1.0]
        high_sus_rate = (len(high_sus_hypo) / len(high_susceptibility_patients)) * 100
        
        print(f"  High susceptibility patients (>1.8): {len(high_susceptibility_patients)}")
        print(f"  - Any hypothyroidism: {len(high_sus_hypo)} ({high_sus_rate:.1f}%)")
        
        if len(high_sus_hypo) > 0:
            high_sus_median = high_sus_hypo['time_to_onset_days'].median()
            print(f"  - Median time to onset: {high_sus_median:.1f} days ({high_sus_median/30:.1f} months)")
    else:
        print("  No high susceptibility patients in dataset")
    
    # Drug-specific analysis
    drug_results = analyze_drug_specific_results()
    
    # Save results
    population_results.to_csv('example_outputs/population_results_4drugs_1000patients.csv', index=False)
    print(f"\n  Results saved to: example_outputs/population_results_4drugs_1000patients.csv")
    
    # Generate comprehensive plots
    print("\n📊 Generating comprehensive population plots...")
    plot_population_results(population_results, save_dir="example_outputs")
    
    print("\n5. Model Parameters Summary")
    print("-" * 60)
    model = QSPModel()
    print(f"  - Base damage threshold: {model.params.base_damage_threshold}")
    print(f"  - Damage threshold growth rate: {model.params.damage_threshold_growth_rate} per month")
    print(f"  - Damage accumulation rate: IFN/{model.params.damage_accumulation_rate}")
    print(f"  - Damage decay rate: {model.params.damage_decay_rate}")
    print(f"  - Damage susceptibility range: {model.params.min_damage_susceptibility} - {model.params.max_damage_susceptibility}")
    print(f"  - Simulation period: 24 months (730 days)")
    
    # Overall validation summary
    print("\n6. Overall Validation Summary")
    print("-" * 60)
    
    any_rate_valid = validation['incidence_rates']['any_rate_within_range']
    grade2_rate_valid = validation['incidence_rates']['grade2_rate_within_range']
    timing_valid = validation['time_to_onset']['onset_within_range'] if not np.isnan(validation['time_to_onset']['model_median_days']) else False
    
    print(f"  ✅ Incidence rate validation: {'PASS' if any_rate_valid else 'FAIL'}")
    print(f"  ✅ Grade 2+ rate validation: {'PASS' if grade2_rate_valid else 'FAIL'}")
    print(f"  ✅ Time to onset validation: {'PASS' if timing_valid else 'FAIL'}")
    
    overall_valid = any_rate_valid and grade2_rate_valid and timing_valid
    print(f"\n  🎯 Overall validation: {'PASS' if overall_valid else 'FAIL'}")
    
    print("\n" + "=" * 80)
    print("FINAL PARAMETER OPTIMIZATION COMPLETE - DRUG-SPECIFIC ANALYSIS PERFORMED")
    print("=" * 80)

if __name__ == "__main__":
    main()
