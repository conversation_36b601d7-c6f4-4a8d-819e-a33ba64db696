# QSP Thyroid Model - Comprehensive Usage Guide

## Table of Contents
1. [Installation Instructions](#installation-instructions)
2. [Quick Start Examples](#quick-start-examples)
3. [Complete API Documentation](#complete-api-documentation)
4. [Plotting Functions](#plotting-functions)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)
7. [Advanced Usage](#advanced-usage)

## Installation Instructions

### Python Environment Setup

#### Prerequisites
- Python 3.8 or higher
- pip package manager

#### Required Dependencies
```bash
# Core scientific computing
pip install numpy>=1.21.0
pip install pandas>=1.3.0
pip install scipy>=1.7.0

# Plotting and visualization
pip install matplotlib>=3.5.0
pip install seaborn>=0.11.0

# Optional: Jupyter for interactive analysis
pip install jupyter
```

#### Installation Steps
```bash
# 1. Clone or download the QSP thyroid model
git clone <repository-url>
cd QSP_PD_thyroid

# 2. Install dependencies
pip install -r requirements.txt

# 3. Verify installation
python -c "from qsp_model_core import QSPModel; print('Installation successful!')"
```

### R Environment Setup

#### Prerequisites
- R 4.0.0 or higher
- RStudio (recommended)

#### Required Packages
```r
# Install required packages
install.packages(c(
  "dplyr",      # Data manipulation
  "ggplot2",    # Plotting
  "R6",         # Object-oriented programming
  "readr",      # Data import/export
  "purrr",      # Functional programming
  "scales",     # Plot scaling
  "gridExtra",  # Plot arrangement
  "viridis",    # Color palettes
  "futile.logger"  # Logging
))
```

#### Installation Steps
```r
# 1. Set working directory to QSP model folder
setwd("path/to/QSP_PD_thyroid")

# 2. Source core functions
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")
source("R/plotting_functions.R")

# 3. Verify installation
model <- QSPModel$new()
cat("R installation successful!\n")
```

## Quick Start Examples

### Python Quick Start

#### Single Patient Simulation
```python
from qsp_model_core import QSPModel, simulate_patient
from plotting_functions import plot_tsh_timecourse

# Create model with calibrated parameters
model = QSPModel()

# Simulate 24-week treatment with nivolumab
results = simulate_patient(
    model=model,
    t_span=(0, 168),  # 24 weeks in days
    drug_type='nivolumab'
)

# Check for hypothyroidism
any_hypo = (results['hypothyroid_grade'] >= 1).any()
grade2_hypo = (results['hypothyroid_grade'] >= 2).any()

print(f"Any hypothyroidism: {any_hypo}")
print(f"Grade 2+ hypothyroidism: {grade2_hypo}")

# Plot TSH progression
fig = plot_tsh_timecourse(results, save_path="tsh_plot.png")
```

#### Population Analysis
```python
from qsp_population_analysis import VirtualCohort
from plotting_functions import plot_incidence_by_drug

# Generate virtual patient cohort
cohort = VirtualCohort(n_patients=100, random_state=42)

# Run population simulation
population_results = cohort.simulate_all(t_span=(0, 168))

# Calculate incidence rates
incidence_rate = population_results['any_hypothyroidism'].mean() * 100
print(f"Population incidence rate: {incidence_rate:.1f}%")

# Plot results
fig = plot_incidence_by_drug(population_results, save_path="incidence_plot.png")
```

### R Quick Start

#### Single Patient Simulation
```r
source("R/qsp_model_core.R")
source("R/plotting_functions.R")

# Create model with calibrated parameters
model <- QSPModel$new()

# Simulate 24-week treatment with pembrolizumab
results <- model$simulate_patient(
  t_span = c(0, 168),  # 24 weeks in days
  drug_type = 'pembrolizumab'
)

# Check for hypothyroidism
any_hypo <- any(results$hypothyroid_grade >= 1)
grade2_hypo <- any(results$hypothyroid_grade >= 2)

cat("Any hypothyroidism:", any_hypo, "\n")
cat("Grade 2+ hypothyroidism:", grade2_hypo, "\n")

# Plot TSH progression
p <- plot_tsh_timecourse(results, save_path = "tsh_plot.png")
```

#### Population Analysis
```r
source("R/qsp_population_analysis.R")

# Generate virtual patient cohort
cohort <- VirtualCohort$new(n_patients = 100, random_state = 42)

# Run population simulation
population_results <- cohort$simulate_all(t_span = c(0, 168))

# Calculate incidence rates
incidence_rate <- mean(population_results$any_hypothyroidism) * 100
cat("Population incidence rate:", round(incidence_rate, 1), "%\n")
```

## Complete API Documentation

### Core Classes and Functions

#### QSPModel Class (Python)
```python
class QSPModel:
    """Core QSP model for checkpoint inhibitor-induced hypothyroidism."""
    
    def __init__(self, **kwargs):
        """Initialize model with calibrated parameters."""
        
    def simulate_patient(self, t_span=(0, 168), drug_type='nivolumab'):
        """Simulate single patient."""
        
    def ode_system(self, t, y, drug_type):
        """ODE system for biological processes."""
        
    def drug_concentration(self, t, drug_type):
        """Calculate drug concentration at time t."""
```

#### Key Parameters
| Parameter | Default Value | Description |
|-----------|---------------|-------------|
| `k_death` | 0.030 | Thyrocyte death rate (day⁻¹(pg/mL)⁻¹) |
| `EC50_IFN_death` | 100 | IFN-γ potency for thyrocyte death (pg/mL) |
| `susceptibility_rate` | 0.10 | Fraction of immune-susceptible patients |
| `theta` | 0.5 | TSH response to T3 deficiency |
| `k_metab_TSH` | 0.1 | TSH clearance rate (day⁻¹) |

#### VirtualCohort Class
```python
class VirtualCohort:
    """Generate and simulate virtual patient populations."""
    
    def __init__(self, n_patients=1000, random_state=42):
        """Initialize virtual cohort."""
        
    def simulate_all(self, t_span=(0, 168), save_timeseries=False):
        """Simulate all patients in cohort."""
```

### Expected Outputs

#### Single Patient Results
```python
# Results DataFrame columns:
['time', 'T_eff', 'IFN', 'Thyro', 'T4', 'T3', 'TSH', 
 'drug_concentration', 'hypothyroid_grade', 'thyrocyte_loss_pct']
```

#### Population Results
```python
# Population DataFrame columns:
['patient_id', 'age', 'sex', 'drug_type', 'any_hypothyroidism',
 'grade2_hypothyroidism', 'time_to_onset_days', 'peak_TSH_mIU_per_L',
 'min_T3_pmol_per_L', 'immune_susceptible', 'susceptibility_level', ...]
```

## Plotting Functions

### Available Plot Types

#### Clinical Plots
- `plot_tsh_timecourse()`: TSH progression with CTCAE thresholds
- `plot_t3_timecourse()`: T3 levels with normal ranges
- `plot_drug_pk_profile()`: Drug concentration over time
- `plot_cytokine_response()`: IFN-γ levels and activation
- `plot_thyrocyte_depletion()`: Thyrocyte mass changes

#### Population Analysis Plots
- `plot_incidence_by_drug()`: Hypothyroidism rates by drug type
- `plot_onset_distribution()`: Time-to-onset histograms
- `plot_risk_stratification()`: Risk factors analysis
- `plot_dose_response()`: Drug concentration vs. response

#### Validation Plots
- `plot_literature_comparison()`: Model vs. published data
- `plot_ctcae_validation()`: CTCAE grading accuracy
- `plot_sensitivity_analysis()`: Parameter sensitivity

### Plot Customization
```python
# All plotting functions support:
fig = plot_tsh_timecourse(
    results,
    patient_id="Patient-001",      # Custom patient ID
    save_path="custom_plot.png",   # Save location
    show_grades=True               # Show CTCAE thresholds
)

# Publication-quality settings (300 DPI, proper fonts)
# Consistent color palette across all plots
# Error handling for edge cases
```

## Troubleshooting

### Common Issues and Solutions

#### Issue: "ModuleNotFoundError: No module named 'qsp_model_core'"
**Solution:**
```bash
# Ensure you're in the correct directory
cd QSP_PD_thyroid

# Check Python path
python -c "import sys; print(sys.path)"

# Add current directory to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Issue: "ODE solver failed" or numerical instability
**Possible Causes:**
- Extreme parameter values
- Very short time steps
- Stiff system dynamics

**Solutions:**
```python
# Increase solver tolerances
results = simulate_patient(
    model, 
    rtol=1e-4,  # Increase from 1e-6
    atol=1e-7   # Increase from 1e-9
)

# Check parameter values
print(f"k_death: {model.params.k_death}")
print(f"EC50_IFN_death: {model.params.EC50_IFN_death}")
```

#### Issue: "100% hypothyroidism incidence" (unrealistic results)
**Diagnosis:**
- Check if using old/uncalibrated parameters
- Verify immune susceptibility is properly assigned

**Solution:**
```python
# Verify calibrated parameters
assert model.params.k_death == 0.030
assert model.params.EC50_IFN_death == 100

# Check susceptibility assignment
print(f"Immune susceptible: {model.params.immune_susceptible}")
```

#### Issue: R plotting functions not working
**Solution:**
```r
# Check required packages
required_packages <- c("ggplot2", "dplyr", "scales")
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if(length(missing_packages)) install.packages(missing_packages)

# Verify ggplot2 version
packageVersion("ggplot2")  # Should be >= 3.3.0
```

### Performance Optimization

#### For Large Population Simulations
```python
# Use parallel processing (if available)
import multiprocessing as mp

# Reduce time resolution for faster simulation
results = cohort.simulate_all(
    t_span=(0, 84),        # Shorter time span
    save_timeseries=False  # Don't save full time series
)
```

#### Memory Management
```python
# For very large cohorts (>1000 patients)
import gc

# Process in batches
batch_size = 100
for i in range(0, n_patients, batch_size):
    batch_results = process_batch(i, i+batch_size)
    # Save batch results
    gc.collect()  # Force garbage collection
```

## Best Practices

### Parameter Modification Guidelines

#### ⚠️ Critical Parameters (Modify with Caution)
- `k_death`: Directly affects hypothyroidism incidence
- `EC50_IFN_death`: Controls cytokine sensitivity
- `susceptibility_rate`: Population-level incidence

#### ✅ Safe to Modify
- Patient demographics (age, sex, HLA status)
- Drug dosing parameters
- Simulation time span
- Plot appearance settings

#### Parameter Validation
```python
def validate_parameters(model):
    """Validate parameter ranges for biological plausibility."""
    params = model.params
    
    # Check critical parameters
    assert 0.001 <= params.k_death <= 0.1, "k_death out of range"
    assert 50 <= params.EC50_IFN_death <= 500, "EC50_IFN_death out of range"
    assert 0.05 <= params.susceptibility_rate <= 0.3, "susceptibility_rate out of range"
    
    print("✅ Parameters validated")
```

### Reproducibility Best Practices

#### Always Set Random Seeds
```python
import numpy as np
np.random.seed(42)  # For Python

# In R:
set.seed(42)
```

#### Document Parameter Changes
```python
# Create parameter log
param_log = {
    'k_death': model.params.k_death,
    'EC50_IFN_death': model.params.EC50_IFN_death,
    'modification_date': '2024-01-01',
    'modification_reason': 'Sensitivity analysis'
}
```

#### Version Control
- Use git to track parameter changes
- Tag stable model versions
- Document validation results

### Clinical Application Guidelines

#### Risk Assessment
```python
def assess_patient_risk(age, sex, hla_status, tpo_ab_status, drug_type):
    """Calculate personalized risk score."""
    
    # Create patient-specific model
    model = QSPModel()
    model.params.age = age
    model.params.sex = sex
    # ... set other parameters
    
    # Run simulation
    results = simulate_patient(model, drug_type=drug_type)
    risk = calculate_risk_score(model, results)
    
    return risk
```

#### Monitoring Recommendations
- **High Risk (>20%)**: Monthly thyroid function tests
- **Moderate Risk (5-20%)**: Bi-monthly monitoring
- **Low Risk (<5%)**: Quarterly monitoring

---

**Document Version:** 1.0  
**Last Updated:** 2024  
**Support:** For technical issues, please refer to the troubleshooting section or contact the development team.
