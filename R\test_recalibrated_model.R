#!/usr/bin/env Rscript
#' Test Recalibrated QSP Thyroid Model
#' ===================================
#'
#' This script tests the recalibrated QSP thyroid model parameters
#' to verify that realistic hypothyroidism incidence rates are achieved.
#'
#' Expected results after recalibration:
#' - Nivolumab: ~15% any hypothyroidism, ~8-12% grade 2+
#' - Pembrolizumab: ~12% any hypothyroidism, ~6-10% grade 2+
#' - Atezolizumab: ~8% any hypothyroidism, ~4-6% grade 2+
#' - Durvalumab: ~6% any hypothyroidism, ~3-5% grade 2+
#'
#' <AUTHOR> Modeling Team
#' @date 2024

# Load required libraries (minimal set)
suppressMessages({
  if (!require(dplyr, quietly = TRUE)) {
    cat("Installing dplyr...\n")
    install.packages("dplyr", repos = "https://cran.r-project.org")
    library(dplyr)
  }
  if (!require(futile.logger, quietly = TRUE)) {
    cat("Installing futile.logger...\n")
    install.packages("futile.logger", repos = "https://cran.r-project.org")
    library(futile.logger)
  }
  if (!require(deSolve, quietly = TRUE)) {
    cat("Installing deSolve...\n")
    install.packages("deSolve", repos = "https://cran.r-project.org")
    library(deSolve)
  }
  if (!require(R6, quietly = TRUE)) {
    cat("Installing R6...\n")
    install.packages("R6", repos = "https://cran.r-project.org")
    library(R6)
  }
  if (!require(pracma, quietly = TRUE)) {
    cat("Installing pracma...\n")
    install.packages("pracma", repos = "https://cran.r-project.org")
    library(pracma)
  }
})

# Source QSP model components
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")

# Set random seed for reproducibility
set.seed(42)

main <- function() {
  
  cat(rep("=", 80), "\n", sep = "")
  cat("TESTING RECALIBRATED QSP THYROID MODEL\n")
  cat(rep("=", 80), "\n", sep = "")
  cat("Recalibrated parameters:\n")
  cat("- k_death = 0.003 day⁻¹(pg/mL)⁻¹ (reduced)\n")
  cat("- EC50_IFN_death = 200 pg/mL (increased)\n")
  cat("- epsilon = 0.15 pg cell⁻¹ day⁻¹ (reduced)\n")
  cat("- Drug thresholds increased for stringent activation\n")
  cat("- Target incidence: 6-15% (drug-specific)\n\n")
  
  # ========================================================================
  # PART 1: SINGLE PATIENT TEST FOR EACH DRUG
  # ========================================================================
  
  cat("PART 1: SINGLE PATIENT TESTS\n")
  cat(rep("-", 50), "\n", sep = "")
  
  drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  single_results <- list()
  
  for (drug in drugs) {
    cat("\nTesting", tools::toTitleCase(drug), "...\n")
    
    # Create model with recalibrated parameters
    model <- QSPModel$new()
    
    # Simulate 24-week treatment course
    results <- simulate_patient(
      model = model,
      t_span = c(0, 168),  # 24 weeks
      drug_type = drug
    )
    
    # Calculate risk metrics
    risk <- calculate_risk_score(model, results)
    
    # Store results
    single_results[[drug]] <- risk
    
    # Print summary
    cat("  - Any hypothyroidism:", ifelse(risk$any_hypothyroidism, "Yes", "No"), "\n")
    cat("  - Grade 2+ hypothyroidism:", ifelse(risk$grade2_hypothyroidism, "Yes", "No"), "\n")
    cat("  - Peak TSH:", round(risk$peak_TSH_mIU_per_L, 2), "mIU/L\n")
    cat("  - Max thyrocyte loss:", round(risk$max_thyrocyte_loss_percent, 1), "%\n")
    cat("  - Immune susceptible:", model$params$immune_susceptible, "\n")
    cat("  - Susceptibility level:", model$params$susceptibility_level, "\n")
  }
  
  # ========================================================================
  # PART 2: SMALL POPULATION TEST (n=50)
  # ========================================================================
  
  cat("\n\nPART 2: SMALL POPULATION TEST (n=50)\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Create small virtual cohort
  cat("Generating small virtual patient cohort...\n")
  cohort <- VirtualCohort$new(n_patients = 50, random_state = 42)
  
  # Display cohort characteristics
  patients <- cohort$patients
  cat("\nCohort demographics:\n")
  cat("- Age:", round(mean(patients$age), 1), "±", round(sd(patients$age), 1), "years\n")
  cat("- Female:", round(mean(patients$sex == 'F') * 100, 1), "%\n")
  cat("- HLA-DRB1*03+:", round(mean(patients$HLA_DRB1_03) * 100, 1), "%\n")
  cat("- TPO-Ab+:", round(mean(patients$TPO_Ab_positive) * 100, 1), "%\n")
  
  # Drug distribution
  drug_dist <- table(patients$drug_type)
  cat("\nDrug distribution:\n")
  for (i in seq_along(drug_dist)) {
    drug_name <- names(drug_dist)[i]
    count <- drug_dist[i]
    cat("-", tools::toTitleCase(drug_name), ":", count, "patients\n")
  }
  
  # Run population simulation
  cat("\nRunning population simulation...\n")
  population_results <- cohort$simulate_all(
    t_span = c(0, 168), 
    save_timeseries = FALSE,
    parallel = FALSE
  )
  
  # Calculate overall incidence rates
  any_hypo_rate <- mean(population_results$any_hypothyroidism) * 100
  grade2_hypo_rate <- mean(population_results$grade2_hypothyroidism) * 100
  
  cat("\nOverall incidence rates:\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate, 1), "%\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate, 1), "%\n")
  
  # Drug-specific incidence rates
  cat("\nDrug-specific incidence rates:\n")
  drug_rates <- population_results %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism) * 100,
      grade2_rate = mean(grade2_hypothyroidism) * 100,
      .groups = 'drop'
    )
  
  for (i in 1:nrow(drug_rates)) {
    row <- drug_rates[i, ]
    cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
        round(row$any_rate, 1), "% any,", 
        round(row$grade2_rate, 1), "% grade 2+\n")
  }
  
  # ========================================================================
  # PART 3: VALIDATION AGAINST TARGET RANGES
  # ========================================================================
  
  cat("\n\nPART 3: VALIDATION AGAINST TARGET RANGES\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Define target ranges (realistic clinical expectations)
  target_ranges <- list(
    'nivolumab' = list(any = c(10, 20), grade2 = c(5, 15)),
    'pembrolizumab' = list(any = c(8, 16), grade2 = c(4, 12)),
    'atezolizumab' = list(any = c(5, 12), grade2 = c(2, 8)),
    'durvalumab' = list(any = c(3, 10), grade2 = c(1, 6))
  )
  
  cat("Validation results:\n")
  validation_passed <- TRUE
  
  for (i in 1:nrow(drug_rates)) {
    row <- drug_rates[i, ]
    drug <- row$drug_type
    
    if (drug %in% names(target_ranges)) {
      target <- target_ranges[[drug]]
      any_in_range <- row$any_rate >= target$any[1] && row$any_rate <= target$any[2]
      grade2_in_range <- row$grade2_rate >= target$grade2[1] && row$grade2_rate <= target$grade2[2]
      
      cat("-", tools::toTitleCase(drug), ":\n")
      cat("  Any hypo: ", round(row$any_rate, 1), "% (target: ", target$any[1], "-", target$any[2], "%) - ", 
          ifelse(any_in_range, "PASS", "FAIL"), "\n", sep = "")
      cat("  Grade 2+: ", round(row$grade2_rate, 1), "% (target: ", target$grade2[1], "-", target$grade2[2], "%) - ", 
          ifelse(grade2_in_range, "PASS", "FAIL"), "\n", sep = "")
      
      if (!any_in_range || !grade2_in_range) {
        validation_passed <- FALSE
      }
    }
  }
  
  cat("\n", rep("=", 80), "\n", sep = "")
  cat("RECALIBRATION TEST COMPLETED!\n")
  cat(rep("=", 80), "\n", sep = "")
  cat("Overall validation:", ifelse(validation_passed, "PASSED", "NEEDS FURTHER ADJUSTMENT"), "\n")
  cat("Population size: 50 patients\n")
  cat("Overall incidence:", round(any_hypo_rate, 1), "% any,", round(grade2_hypo_rate, 1), "% grade 2+\n")
  
  if (validation_passed) {
    cat("✓ Model successfully recalibrated to realistic incidence rates!\n")
  } else {
    cat("⚠ Model may need further parameter adjustment.\n")
    cat("Consider:\n")
    cat("- Further reducing k_death parameter\n")
    cat("- Increasing drug activation thresholds\n")
    cat("- Reducing epsilon (cytokine production)\n")
    cat("- Increasing cytokine clearance rate\n")
  }
}

# Run main function if script is executed directly
if (!interactive()) {
  main()
}
