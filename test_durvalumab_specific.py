#!/usr/bin/env python3
"""
Test script specifically for durvalumab parameter tuning
"""

import numpy as np
import pandas as pd
from qsp_model_core import QSPModel, simulate_patient
from qsp_population_analysis import VirtualCohort

# Set random seed for reproducibility
np.random.seed(42)

def test_durvalumab_specific():
    """Test durvalumab-specific parameters with focused analysis."""
    
    print("DURVALUMAB-SPECIFIC PARAMETER TUNING TEST")
    print("=" * 50)
    
    # Create a cohort with more patients to get better statistics
    cohort = VirtualCohort(n_patients=1000, random_state=42)
    
    # Filter for durvalumab patients only
    durvalumab_patients = cohort.patients[cohort.patients['drug_type'] == 'durvalumab']
    print(f"Total durvalumab patients: {len(durvalumab_patients)}")
    
    if len(durvalumab_patients) == 0:
        print("No durvalumab patients found in cohort!")
        return
    
    # Test a few individual durvalumab patients
    print(f"\nTesting individual durvalumab patients:")
    
    hypothyroidism_count = 0
    grade2_count = 0
    
    for i, (idx, patient) in enumerate(durvalumab_patients.head(10).iterrows()):
        print(f"\nPatient {i+1} (ID: {patient['patient_id']}):")
        print(f"  - Age: {patient['age']:.1f}, Sex: {patient['sex']}")
        print(f"  - HLA-DRB1*03: {patient['HLA_DRB1_03']}, TPO-Ab+: {patient['TPO_Ab_positive']}")
        
        # Create model and simulate
        model = QSPModel()
        
        # Set patient-specific parameters
        model.params.sex_factor = patient['sex_factor']
        model.params.age_factor = patient['age_factor'] 
        model.params.HLA_factor = patient['HLA_factor']
        model.params.TPO_Ab_titer = patient['TPO_Ab_titer']
        
        # Assign immune susceptibility
        model._assign_immune_susceptibility('durvalumab')
        
        print(f"  - Immune susceptible: {model.params.immune_susceptible}")
        print(f"  - Susceptibility level: {model.params.susceptibility_level}")
        
        # Simulate
        results = simulate_patient(model, t_span=(0, 168), drug_type='durvalumab')

        # Check drug concentrations
        max_drug_conc = results['drug_concentration'].max()
        print(f"  - Max drug concentration: {max_drug_conc:.1f} ng/mL")

        # Check available columns
        print(f"  - Available columns: {list(results.columns)}")

        # Check for hypothyroidism - use correct column names
        if 'TSH' in results.columns:
            final_tsh = results['TSH'].iloc[-1]
            max_tsh = results['TSH'].max()
        elif 'TSH_mIU_L' in results.columns:
            final_tsh = results['TSH_mIU_L'].iloc[-1]
            max_tsh = results['TSH_mIU_L'].max()
        else:
            print("  - TSH column not found, skipping hypothyroidism check")
            continue

        any_hypo = max_tsh > 4.5  # Any hypothyroidism
        grade2_hypo = max_tsh > 10.0  # Grade 2+ hypothyroidism
        
        print(f"  - Final TSH: {final_tsh:.2f} mIU/L, Max TSH: {max_tsh:.2f} mIU/L")
        print(f"  - Any hypothyroidism: {any_hypo}, Grade 2+: {grade2_hypo}")
        
        if any_hypo:
            hypothyroidism_count += 1
        if grade2_hypo:
            grade2_count += 1
    
    print(f"\nSummary for 10 durvalumab patients:")
    print(f"- Any hypothyroidism: {hypothyroidism_count}/10 ({hypothyroidism_count*10}%)")
    print(f"- Grade 2+ hypothyroidism: {grade2_count}/10 ({grade2_count*10}%)")
    
    # Now run full population simulation for durvalumab patients only
    print(f"\nRunning full durvalumab population simulation...")
    
    # Create a cohort with only durvalumab patients
    durvalumab_cohort = VirtualCohort(n_patients=200, random_state=42)
    # Force all patients to be durvalumab
    durvalumab_cohort.patients['drug_type'] = 'durvalumab'
    
    results = durvalumab_cohort.simulate_all(t_span=(0, 168), save_timeseries=False)
    
    any_hypo_rate = results['any_hypothyroidism'].mean() * 100
    grade2_hypo_rate = results['grade2_hypothyroidism'].mean() * 100
    
    print(f"\nDurvalumab population results (n=200):")
    print(f"- Any hypothyroidism: {any_hypo_rate:.1f}%")
    print(f"- Grade 2+ hypothyroidism: {grade2_hypo_rate:.1f}%")
    print(f"- Literature target: 5.0% any, 1.8% grade 2+")
    
    if abs(any_hypo_rate - 5.0) < 1.0:
        print("✅ Any hypothyroidism rate is close to literature target!")
    else:
        print(f"❌ Any hypothyroidism rate needs adjustment (current: {any_hypo_rate:.1f}%, target: 5.0%)")
    
    if abs(grade2_hypo_rate - 1.8) < 1.0:
        print("✅ Grade 2+ hypothyroidism rate is close to literature target!")
    else:
        print(f"❌ Grade 2+ hypothyroidism rate needs adjustment (current: {grade2_hypo_rate:.1f}%, target: 1.8%)")

if __name__ == "__main__":
    test_durvalumab_specific()