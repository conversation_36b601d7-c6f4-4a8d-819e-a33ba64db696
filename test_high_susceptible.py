from qsp_model_core import QSPModel, simulate_patient, calculate_risk_score, ModelParameters

# Test high-susceptible patients specifically
print('Testing high-susceptible patients:')
print('='*50)

# Force a high-susceptible patient
params = ModelParameters()
model = QSPModel(params)
model.params.immune_susceptible = True
model.params.susceptibility_level = 'high'
model.params.susceptibility_assigned = True
model._apply_covariates()

print(f'High-susceptible patient parameters:')
print(f'  k_death: {model.params.k_death}')
print(f'  epsilon: {model.params.epsilon}')
print(f'  EC50_IFN_death: {model.params.EC50_IFN_death}')

sim_result = simulate_patient(model, t_span=(0, 168), drug_type='nivolumab')
risk_metrics = calculate_risk_score(model, sim_result, time_horizon=168)

print(f'\nResults:')
print(f'  immune_susceptible: {risk_metrics["immune_susceptible"]}')
print(f'  susceptibility_level: {risk_metrics["susceptibility_level"]}')
print(f'  any_hypothyroidism: {risk_metrics["any_hypothyroidism"]}')
print(f'  peak_TSH: {risk_metrics["peak_TSH_mIU_per_L"]:.2f}')
print(f'  peak_IFN: {risk_metrics["peak_IFN_pg_per_mL"]:.1f}')
print(f'  max_thyrocyte_loss: {risk_metrics["max_thyrocyte_loss_percent"]:.1f}%')

# Test low-susceptible patient
print('\n' + '='*50)
print('Testing low-susceptible patients:')

params = ModelParameters()
model = QSPModel(params)
model.params.immune_susceptible = True
model.params.susceptibility_level = 'low'
model.params.susceptibility_assigned = True
model._apply_covariates()

print(f'Low-susceptible patient parameters:')
print(f'  k_death: {model.params.k_death}')
print(f'  epsilon: {model.params.epsilon}')
print(f'  EC50_IFN_death: {model.params.EC50_IFN_death}')

sim_result = simulate_patient(model, t_span=(0, 168), drug_type='nivolumab')
risk_metrics = calculate_risk_score(model, sim_result, time_horizon=168)

print(f'\nResults:')
print(f'  immune_susceptible: {risk_metrics["immune_susceptible"]}')
print(f'  susceptibility_level: {risk_metrics["susceptibility_level"]}')
print(f'  any_hypothyroidism: {risk_metrics["any_hypothyroidism"]}')
print(f'  peak_TSH: {risk_metrics["peak_TSH_mIU_per_L"]:.2f}')
print(f'  peak_IFN: {risk_metrics["peak_IFN_pg_per_mL"]:.1f}')
print(f'  max_thyrocyte_loss: {risk_metrics["max_thyrocyte_loss_percent"]:.1f}%')
