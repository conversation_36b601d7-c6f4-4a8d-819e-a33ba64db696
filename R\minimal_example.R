#!/usr/bin/env Rscript
#' QSP Thyroid Model - Minimal Example (No Plotting Dependencies)
#' ==============================================================
#'
#' This script demonstrates the QSP thyroid model functionality
#' without requiring plotting packages that might cause issues.
#' Use this if the full example_usage.R fails due to missing packages.
#'
#' <AUTHOR> Modeling Team
#' @date 2024

# Load only essential libraries
suppressMessages({
  library(dplyr)
  library(futile.logger)
})

# Source QSP model components (skip plotting functions)
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")

# Set random seed for reproducibility
set.seed(42)

main <- function() {
  
  cat(rep("=", 80), "\n", sep = "")
  cat("QSP THYROID MODEL - MINIMAL EXAMPLE\n")
  cat(rep("=", 80), "\n", sep = "")
  cat("Recalibrated parameters for realistic incidence:\n")
  cat("- k_death = 0.003 day⁻¹(pg/mL)⁻¹ (reduced)\n")
  cat("- EC50_IFN_death = 200 pg/mL (increased)\n")
  cat("- epsilon = 0.15 pg cell⁻¹ day⁻¹ (reduced)\n")
  cat("- Target incidence: 6-15% (drug-specific)\n\n")
  
  # Create output directory
  output_dir <- "minimal_outputs_R"
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # ========================================================================
  # PART 1: SINGLE PATIENT SIMULATIONS
  # ========================================================================
  
  cat("PART 1: SINGLE PATIENT SIMULATIONS\n")
  cat(rep("-", 50), "\n", sep = "")
  
  drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  single_patient_results <- list()
  
  for (drug in drugs) {
    cat("\nSimulating patient with", tools::toTitleCase(drug), "...\n")
    
    # Create model with recalibrated parameters
    model <- QSPModel$new()
    
    # Simulate 24-week treatment course
    results <- simulate_patient(
      model = model,
      t_span = c(0, 168),  # 24 weeks
      drug_type = drug
    )
    
    # Calculate risk metrics
    risk <- calculate_risk_score(model, results)
    
    # Store results
    single_patient_results[[drug]] <- list(
      results = results,
      risk = risk,
      model = model
    )
    
    # Print summary
    cat("  - Any hypothyroidism:", ifelse(risk$any_hypothyroidism, "Yes", "No"), "\n")
    cat("  - Grade 2+ hypothyroidism:", ifelse(risk$grade2_hypothyroidism, "Yes", "No"), "\n")
    if (!is.na(risk$time_to_onset_days)) {
      cat("  - Time to onset:", round(risk$time_to_onset_days, 1), "days\n")
    }
    cat("  - Peak TSH:", round(risk$peak_TSH_mIU_per_L, 2), "mIU/L\n")
    cat("  - Max thyrocyte loss:", round(risk$max_thyrocyte_loss_percent, 1), "%\n")
    cat("  - Immune susceptible:", model$params$immune_susceptible, "\n")
    cat("  - Susceptibility level:", model$params$susceptibility_level, "\n")
    
    # Save individual results
    write.csv(results, file.path(output_dir, paste0("single_patient_", drug, "_timeseries.csv")), row.names = FALSE)
  }
  
  # ========================================================================
  # PART 2: POPULATION SIMULATION
  # ========================================================================
  
  cat("\n\nPART 2: POPULATION SIMULATION (n=100)\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Create virtual cohort with realistic demographics
  cat("Generating virtual patient cohort...\n")
  cohort <- VirtualCohort$new(n_patients = 1000, random_state = 42)
  
  # Display cohort characteristics
  patients <- cohort$patients
  cat("\nCohort demographics:\n")
  cat("- Age:", round(mean(patients$age), 1), "±", round(sd(patients$age), 1), "years\n")
  cat("- Female:", round(mean(patients$sex == 'F') * 100, 1), "%\n")
  cat("- HLA-DRB1*03+:", round(mean(patients$HLA_DRB1_03) * 100, 1), "%\n")
  cat("- TPO-Ab+:", round(mean(patients$TPO_Ab_positive) * 100, 1), "%\n")
  
  # Drug distribution
  drug_dist <- table(patients$drug_type)
  cat("\nDrug distribution:\n")
  for (i in seq_along(drug_dist)) {
    drug_name <- names(drug_dist)[i]
    count <- drug_dist[i]
    cat("-", tools::toTitleCase(drug_name), ":", count, "patients (", 
        round(count/100*100), "%)\n", sep = "")
  }
  
  # Run population simulation
  cat("\nRunning population simulation...\n")
  population_results <- cohort$simulate_all(
    t_span = c(0, 168), 
    save_timeseries = FALSE,
    parallel = FALSE  # Keep simple for troubleshooting
  )
  
  # Calculate overall incidence rates
  any_hypo_rate <- mean(population_results$any_hypothyroidism) * 100
  grade2_hypo_rate <- mean(population_results$grade2_hypothyroidism) * 100
  
  cat("\nOverall incidence rates:\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate, 1), "%\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate, 1), "%\n")
  
  # Drug-specific incidence rates
  cat("\nDrug-specific incidence rates:\n")
  drug_rates <- population_results %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism) * 100,
      grade2_rate = mean(grade2_hypothyroidism) * 100,
      .groups = 'drop'
    )
  
  for (i in 1:nrow(drug_rates)) {
    row <- drug_rates[i, ]
    cat("-", tools::toTitleCase(row$drug_type), ":", 
        round(row$any_rate, 1), "% any,", 
        round(row$grade2_rate, 1), "% grade 2+\n")
  }
  
  # ========================================================================
  # PART 3: VALIDATION AGAINST TARGET RANGES
  # ========================================================================
  
  cat("\n\nPART 3: VALIDATION AGAINST TARGET RANGES\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Define target ranges (realistic clinical expectations)
  target_ranges <- list(
    'nivolumab' = list(any = c(10, 20), grade2 = c(5, 15)),
    'pembrolizumab' = list(any = c(8, 16), grade2 = c(4, 12)),
    'atezolizumab' = list(any = c(5, 12), grade2 = c(2, 8)),
    'durvalumab' = list(any = c(3, 10), grade2 = c(1, 6))
  )
  
  cat("Validation results:\n")
  validation_passed <- TRUE
  
  for (i in 1:nrow(drug_rates)) {
    row <- drug_rates[i, ]
    drug <- row$drug_type
    
    if (drug %in% names(target_ranges)) {
      target <- target_ranges[[drug]]
      any_in_range <- row$any_rate >= target$any[1] && row$any_rate <= target$any[2]
      grade2_in_range <- row$grade2_rate >= target$grade2[1] && row$grade2_rate <= target$grade2[2]
      
      cat("-", tools::toTitleCase(drug), ":\n")
      cat("  Any hypo: ", round(row$any_rate, 1), "% (target: ", target$any[1], "-", target$any[2], "%) - ", 
          ifelse(any_in_range, "PASS", "FAIL"), "\n", sep = "")
      cat("  Grade 2+: ", round(row$grade2_rate, 1), "% (target: ", target$grade2[1], "-", target$grade2[2], "%) - ", 
          ifelse(grade2_in_range, "PASS", "FAIL"), "\n", sep = "")
      
      if (!any_in_range || !grade2_in_range) {
        validation_passed <- FALSE
      }
    }
  }
  
  # ========================================================================
  # PART 4: SAVE RESULTS AND SUMMARY
  # ========================================================================
  
  cat("\n\nPART 4: SAVING RESULTS AND SUMMARY\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Save population results
  write.csv(population_results, file.path(output_dir, "population_results.csv"), row.names = FALSE)
  cat("- Population results saved to:", file.path(output_dir, "population_results.csv"), "\n")
  
  # Create summary report
  summary_file <- file.path(output_dir, "analysis_summary.txt")
  
  summary_text <- paste0(
    "QSP THYROID MODEL - MINIMAL EXAMPLE SUMMARY\n",
    paste(rep("=", 50), collapse = ""), "\n\n",
    "Recalibrated Model Parameters:\n",
    "- k_death = 0.003 day⁻¹(pg/mL)⁻¹ (reduced for realistic incidence)\n",
    "- EC50_IFN_death = 200 pg/mL (increased threshold)\n",
    "- epsilon = 0.15 pg cell⁻¹ day⁻¹ (reduced cytokine production)\n",
    "- Drug thresholds increased for more stringent activation\n\n",
    "Population Results (n=100):\n",
    "- Overall any hypothyroidism: ", round(any_hypo_rate, 1), "%\n",
    "- Overall grade 2+ hypothyroidism: ", round(grade2_hypo_rate, 1), "%\n\n",
    "Drug-Specific Results:\n"
  )
  
  for (i in 1:nrow(drug_rates)) {
    row <- drug_rates[i, ]
    summary_text <- paste0(summary_text,
      "- ", tools::toTitleCase(row$drug_type), ": ", 
      round(row$any_rate, 1), "% any, ", 
      round(row$grade2_rate, 1), "% grade 2+\n")
  }
  
  summary_text <- paste0(summary_text,
    "\nValidation Status: ", ifelse(validation_passed, "PASSED", "NEEDS ADJUSTMENT"), "\n",
    "Timestamp: ", format(Sys.time()), "\n"
  )
  
  writeLines(summary_text, summary_file)
  cat("- Summary report saved to:", summary_file, "\n")
  
  cat("\n", rep("=", 80), "\n", sep = "")
  cat("MINIMAL EXAMPLE COMPLETED SUCCESSFULLY!\n")
  cat(rep("=", 80), "\n", sep = "")
  cat("All outputs saved to:", output_dir, "\n")
  cat("- Population analysis: 100 patients\n")
  cat("- Incidence rates:", round(any_hypo_rate, 1), "% any,", round(grade2_hypo_rate, 1), "% grade 2+\n")
  cat("- Model validation:", ifelse(validation_passed, "PASSED", "NEEDS FURTHER ADJUSTMENT"), "\n")
  
  if (validation_passed) {
    cat("✓ Recalibration successful - realistic incidence rates achieved!\n")
  } else {
    cat("⚠ Some rates outside target ranges - may need further parameter tuning\n")
  }
}

# Run main function if script is executed directly
if (!interactive()) {
  main()
}
