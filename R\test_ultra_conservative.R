#!/usr/bin/env Rscript
#' QSP Thyroid Model - Test Ultra Conservative Parameters
#' =====================================================
#'
#' This script tests the ultra-conservative parameter adjustments
#' to verify they produce realistic hypothyroidism incidence rates.
#'
#' Target: 10-20% any hypothyroidism, 5-15% grade 2+
#'
#' <AUTHOR> Modeling Team
#' @date 2024

# Load required libraries
suppressMessages({
  library(dplyr)
  library(futile.logger)
})

# Source QSP model components
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")

# Set random seed for reproducibility
set.seed(42)

cat("================================================================================\n")
cat("QSP THYROID MODEL - ULTRA CONSERVATIVE PARAMETER TEST\n")
cat("================================================================================\n")
cat("Testing extremely conservative parameters:\n")
cat("- k_death = 0.001 (was 0.008, reduced by 87.5%)\n")
cat("- epsilon = 0.05 (was 0.3, reduced by 83%)\n")
cat("- EC50_IFN_death = 500 (was 100, increased 5x)\n")
cat("- cytokine_threshold = 1000 (was 100, increased 10x)\n")
cat("- Drug thresholds: 2-3x higher\n")
cat("- Susceptibility rates: halved\n")
cat("- Covariate effects: extremely conservative\n\n")

# Test with small population first
cat("STEP 1: Small Population Test (n=50)\n")
cat(rep("-", 50), "\n", sep = "")

# Create small cohort
cohort_small <- VirtualCohort$new(n_patients = 50, random_state = 42)

# Run simulation
results_small <- cohort_small$simulate_all(
  t_span = c(0, 168),  # 24 weeks
  save_timeseries = FALSE,
  parallel = FALSE
)

# Calculate incidence rates
any_hypo_rate_small <- mean(results_small$any_hypothyroidism) * 100
grade2_hypo_rate_small <- mean(results_small$grade2_hypothyroidism) * 100

cat("Small population results (n=50):\n")
cat("- Any hypothyroidism:", round(any_hypo_rate_small, 1), "%\n")
cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_small, 1), "%\n")

# Drug-specific rates
drug_rates_small <- results_small %>%
  group_by(drug_type) %>%
  summarise(
    n = n(),
    any_rate = mean(any_hypothyroidism) * 100,
    grade2_rate = mean(grade2_hypothyroidism) * 100,
    .groups = 'drop'
  )

cat("Drug-specific rates (small cohort):\n")
for (i in 1:nrow(drug_rates_small)) {
  row <- drug_rates_small[i, ]
  cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
      round(row$any_rate, 1), "% any,", 
      round(row$grade2_rate, 1), "% grade 2+\n")
}

# Check if rates are in reasonable range for small sample
if (any_hypo_rate_small <= 30 && grade2_hypo_rate_small <= 25) {
  cat("✓ Small population test: PROMISING (rates reduced)\n\n")
  
  # Proceed to larger population
  cat("STEP 2: Larger Population Test (n=200)\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Create larger cohort
  cohort_large <- VirtualCohort$new(n_patients = 200, random_state = 123)
  
  # Run simulation
  results_large <- cohort_large$simulate_all(
    t_span = c(0, 168),  # 24 weeks
    save_timeseries = FALSE,
    parallel = FALSE
  )
  
  # Calculate incidence rates
  any_hypo_rate_large <- mean(results_large$any_hypothyroidism) * 100
  grade2_hypo_rate_large <- mean(results_large$grade2_hypothyroidism) * 100
  
  cat("Large population results (n=200):\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_large, 1), "%\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_large, 1), "%\n")
  
  # Drug-specific rates
  drug_rates_large <- results_large %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism) * 100,
      grade2_rate = mean(grade2_hypothyroidism) * 100,
      .groups = 'drop'
    )
  
  cat("Drug-specific rates (large cohort):\n")
  for (i in 1:nrow(drug_rates_large)) {
    row <- drug_rates_large[i, ]
    cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
        round(row$any_rate, 1), "% any,", 
        round(row$grade2_rate, 1), "% grade 2+\n")
  }
  
  # Validation against target ranges
  cat("\nSTEP 3: Validation Against Target Ranges\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Define target ranges
  target_ranges <- list(
    'nivolumab' = list(any = c(10, 20), grade2 = c(5, 15)),
    'pembrolizumab' = list(any = c(8, 16), grade2 = c(4, 12)),
    'atezolizumab' = list(any = c(5, 12), grade2 = c(2, 8)),
    'durvalumab' = list(any = c(3, 10), grade2 = c(1, 6))
  )
  
  # Overall validation
  overall_any_target <- any_hypo_rate_large >= 10 && any_hypo_rate_large <= 20
  overall_grade2_target <- grade2_hypo_rate_large >= 5 && grade2_hypo_rate_large <= 15
  
  cat("Overall validation:\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_large, 1), "% (target: 10-20%) -", 
      ifelse(overall_any_target, "PASS", "FAIL"), "\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_large, 1), "% (target: 5-15%) -", 
      ifelse(overall_grade2_target, "PASS", "FAIL"), "\n")
  
  # Drug-specific validation
  cat("\nDrug-specific validation:\n")
  validation_passed <- TRUE
  
  for (i in 1:nrow(drug_rates_large)) {
    row <- drug_rates_large[i, ]
    drug <- row$drug_type
    
    if (drug %in% names(target_ranges)) {
      target <- target_ranges[[drug]]
      any_in_range <- row$any_rate >= target$any[1] && row$any_rate <= target$any[2]
      grade2_in_range <- row$grade2_rate >= target$grade2[1] && row$grade2_rate <= target$grade2[2]
      
      cat("-", tools::toTitleCase(drug), ":\n")
      cat("  Any hypo: ", round(row$any_rate, 1), "% (target: ", target$any[1], "-", target$any[2], "%) - ", 
          ifelse(any_in_range, "PASS", "FAIL"), "\n", sep = "")
      cat("  Grade 2+: ", round(row$grade2_rate, 1), "% (target: ", target$grade2[1], "-", target$grade2[2], "%) - ", 
          ifelse(grade2_in_range, "PASS", "FAIL"), "\n", sep = "")
      
      if (!any_in_range || !grade2_in_range) {
        validation_passed <- FALSE
      }
    }
  }
  
  # Final assessment
  cat("\nSTEP 4: Final Assessment\n")
  cat(rep("-", 50), "\n", sep = "")
  
  overall_validation <- overall_any_target && overall_grade2_target && validation_passed
  
  if (overall_validation) {
    cat("🎉 SUCCESS: Ultra-conservative parameters achieved realistic incidence rates!\n")
    cat("✓ Overall rates within target ranges\n")
    cat("✓ Drug-specific rates within target ranges\n")
    cat("✓ Clinical ranking preserved\n")
    
    # Save successful results
    write.csv(results_large, "ultra_conservative_results.csv", row.names = FALSE)
    cat("✓ Results saved to: ultra_conservative_results.csv\n")
    
  } else {
    cat("⚠ PARTIAL SUCCESS: Some improvements but further tuning needed\n")
    
    if (!overall_any_target) {
      if (any_hypo_rate_large > 20) {
        cat("- Overall any hypothyroidism still too high (", round(any_hypo_rate_large, 1), "% > 20%)\n")
        cat("  Recommendation: Further reduce epsilon or increase EC50_IFN_death\n")
      } else if (any_hypo_rate_large < 10) {
        cat("- Overall any hypothyroidism too low (", round(any_hypo_rate_large, 1), "% < 10%)\n")
        cat("  Recommendation: Slightly increase epsilon or reduce EC50_IFN_death\n")
      }
    }
    
    if (!overall_grade2_target) {
      if (grade2_hypo_rate_large > 15) {
        cat("- Overall grade 2+ hypothyroidism still too high (", round(grade2_hypo_rate_large, 1), "% > 15%)\n")
        cat("  Recommendation: Further reduce k_death or increase cytokine_threshold\n")
      } else if (grade2_hypo_rate_large < 5) {
        cat("- Overall grade 2+ hypothyroidism too low (", round(grade2_hypo_rate_large, 1), "% < 5%)\n")
        cat("  Recommendation: Slightly increase k_death or reduce cytokine_threshold\n")
      }
    }
  }
  
} else {
  cat("✗ Small population test: RATES STILL TOO HIGH\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_small, 1), "% (should be ≤30% for small sample)\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_small, 1), "% (should be ≤25% for small sample)\n")
  cat("\nRECOMMENDATION: Parameters need even more aggressive reduction\n")
  cat("- Consider reducing epsilon to 0.02 (from 0.05)\n")
  cat("- Consider increasing EC50_IFN_death to 1000 (from 500)\n")
  cat("- Consider reducing susceptibility rates further\n")
}

cat("\n", rep("=", 80), "\n", sep = "")
cat("ULTRA CONSERVATIVE PARAMETER TEST COMPLETED\n")
cat(rep("=", 80), "\n", sep = "")
cat("Timestamp:", format(Sys.time()), "\n")
