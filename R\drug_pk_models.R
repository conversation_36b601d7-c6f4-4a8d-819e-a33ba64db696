#' @title Drug Pharmacokinetic Models
#' @description Pharmacokinetic models for checkpoint inhibitors with accurate 
#' clearance and distribution parameters for nivolumab, pembrolizumab, and atezolizumab.
#' 
#' <AUTHOR> Modeling Team
#' @docType package
#' @name drug_pk_models
NULL

#' @title Drug PK Parameters Class
#' @description Container for drug-specific pharmacokinetic parameters
#' @export
DrugPKParameters <- R6::R6Class(
  "DrugPKParameters",
  public = list(
    
    #' @field drug_name Name of the drug
    drug_name = NULL,
    
    #' @field clearance Systemic clearance (L/day)
    clearance = NULL,
    
    #' @field volume_central Central volume of distribution (L)
    volume_central = NULL,
    
    #' @field volume_peripheral Peripheral volume of distribution (L)
    volume_peripheral = NULL,
    
    #' @field intercompartmental_clearance Q (L/day)
    intercompartmental_clearance = NULL,
    
    #' @field standard_dose Standard clinical dose (mg)
    standard_dose = NULL,
    
    #' @field dosing_interval Standard dosing interval (days)
    dosing_interval = NULL,
    
    #' @field molecular_weight Molecular weight (kDa)
    molecular_weight = NULL,
    
    #' @field bioavailability Bioavailability (fraction)
    bioavailability = 1.0,
    
    #' @field protein_binding Protein binding (fraction)
    protein_binding = NULL,
    
    #' @description Initialize drug PK parameters
    #' @param drug_name Name of the drug
    initialize = function(drug_name) {
      self$drug_name <- drug_name
      private$set_drug_parameters(drug_name)
    },
    
    #' @description Print PK parameter summary
    print = function() {
      cat("Drug PK Parameters for", self$drug_name, ":\n")
      cat("- Clearance:", self$clearance, "L/day\n")
      cat("- Volume (central):", self$volume_central, "L\n")
      cat("- Standard dose:", self$standard_dose, "mg\n")
      cat("- Dosing interval:", self$dosing_interval, "days\n")
      cat("- Molecular weight:", self$molecular_weight, "kDa\n")
    }
  ),
  
  private = list(
    #' @description Set drug-specific parameters
    #' @param drug_name Name of the drug
    set_drug_parameters = function(drug_name) {
      if (tolower(drug_name) == 'nivolumab') {
        self$clearance <- 0.2  # L/day
        self$volume_central <- 6.0  # L
        self$volume_peripheral <- 4.2  # L
        self$intercompartmental_clearance <- 0.8  # L/day
        self$standard_dose <- 240  # mg q2w
        self$dosing_interval <- 14  # days
        self$molecular_weight <- 146  # kDa
        self$protein_binding <- 0.95
        
      } else if (tolower(drug_name) == 'pembrolizumab') {
        self$clearance <- 0.22  # L/day
        self$volume_central <- 6.0  # L
        self$volume_peripheral <- 4.5  # L
        self$intercompartmental_clearance <- 0.9  # L/day
        self$standard_dose <- 200  # mg q3w
        self$dosing_interval <- 21  # days
        self$molecular_weight <- 149  # kDa
        self$protein_binding <- 0.94
        
      } else if (tolower(drug_name) == 'atezolizumab') {
        self$clearance <- 0.25  # L/day
        self$volume_central <- 6.5  # L
        self$volume_peripheral <- 5.0  # L
        self$intercompartmental_clearance <- 1.0  # L/day
        self$standard_dose <- 1200  # mg q3w
        self$dosing_interval <- 21  # days
        self$molecular_weight <- 145  # kDa
        self$protein_binding <- 0.93
        
      } else if (tolower(drug_name) == 'durvalumab') {
        self$clearance <- 0.23  # L/day
        self$volume_central <- 6.2  # L
        self$volume_peripheral <- 4.8  # L
        self$intercompartmental_clearance <- 0.85  # L/day
        self$standard_dose <- 1500  # mg q4w
        self$dosing_interval <- 28  # days
        self$molecular_weight <- 147  # kDa
        self$protein_binding <- 0.92
        
      } else {
        stop("Unknown drug: ", drug_name, ". Supported drugs: nivolumab, pembrolizumab, atezolizumab, durvalumab")
      }
    }
  )
)

#' @title One-Compartment PK Model
#' @description Simple one-compartment pharmacokinetic model
#' @param time Time points (days)
#' @param dose Dose amount (mg)
#' @param clearance Clearance (L/day)
#' @param volume Volume of distribution (L)
#' @param dosing_times Vector of dosing times (days)
#' @return Drug concentrations (ng/mL)
#' @export
pk_one_compartment <- function(time, dose, clearance, volume, dosing_times = 0) {
  concentrations <- numeric(length(time))
  
  for (i in seq_along(time)) {
    t <- time[i]
    conc <- 0
    
    for (dose_time in dosing_times) {
      if (dose_time <= t) {
        time_since_dose <- t - dose_time
        # Convert mg to ng and calculate concentration
        conc <- conc + (dose * 1e6 / volume) * exp(-clearance * time_since_dose / volume)
      }
    }
    
    concentrations[i] <- max(conc, 0)
  }
  
  return(concentrations)
}

#' @title Two-Compartment PK Model
#' @description Two-compartment pharmacokinetic model with central and peripheral compartments
#' @param time Time points (days)
#' @param dose Dose amount (mg)
#' @param clearance Central clearance (L/day)
#' @param volume_central Central volume (L)
#' @param volume_peripheral Peripheral volume (L)
#' @param intercompartmental_clearance Q (L/day)
#' @param dosing_times Vector of dosing times (days)
#' @return Drug concentrations in central compartment (ng/mL)
#' @export
pk_two_compartment <- function(time, dose, clearance, volume_central, 
                               volume_peripheral, intercompartmental_clearance, 
                               dosing_times = 0) {
  
  # Calculate hybrid rate constants
  k10 <- clearance / volume_central
  k12 <- intercompartmental_clearance / volume_central
  k21 <- intercompartmental_clearance / volume_peripheral
  
  # Calculate alpha and beta
  a <- k10 + k12 + k21
  b <- k10 * k21
  
  alpha <- (a + sqrt(a^2 - 4*b)) / 2
  beta <- (a - sqrt(a^2 - 4*b)) / 2
  
  # Calculate coefficients A and B
  A <- (k21 - alpha) / (beta - alpha)
  B <- (alpha - k21) / (beta - alpha)
  
  concentrations <- numeric(length(time))
  
  for (i in seq_along(time)) {
    t <- time[i]
    conc <- 0
    
    for (dose_time in dosing_times) {
      if (dose_time <= t) {
        time_since_dose <- t - dose_time
        # Bi-exponential equation
        dose_ng <- dose * 1e6  # Convert mg to ng
        conc <- conc + (dose_ng / volume_central) * (A * exp(-alpha * time_since_dose) + 
                                                     B * exp(-beta * time_since_dose))
      }
    }
    
    concentrations[i] <- max(conc, 0)
  }
  
  return(concentrations)
}

#' @title Generate Dosing Schedule
#' @description Generate dosing times for a given regimen
#' @param start_time Start time (days)
#' @param end_time End time (days)
#' @param dosing_interval Dosing interval (days)
#' @return Vector of dosing times
#' @export
generate_dosing_schedule <- function(start_time = 0, end_time = 168, dosing_interval = 14) {
  dosing_times <- seq(start_time, end_time, by = dosing_interval)
  return(dosing_times)
}

#' @title Calculate Drug Concentration with PK Model
#' @description Calculate drug concentration using appropriate PK model
#' @param time Time points (days)
#' @param drug_name Name of the drug
#' @param dose_override Optional dose override (mg)
#' @param pk_model Type of PK model ("one_compartment" or "two_compartment")
#' @param dosing_schedule Optional custom dosing schedule
#' @return Drug concentrations (ng/mL)
#' @export
calculate_drug_concentration <- function(time, drug_name, dose_override = NULL, 
                                        pk_model = "one_compartment", 
                                        dosing_schedule = NULL) {
  
  # Get drug parameters
  pk_params <- DrugPKParameters$new(drug_name)
  
  # Use override dose if provided
  dose <- if (!is.null(dose_override)) dose_override else pk_params$standard_dose
  
  # Generate dosing schedule if not provided
  if (is.null(dosing_schedule)) {
    dosing_schedule <- generate_dosing_schedule(
      start_time = 0, 
      end_time = max(time) + pk_params$dosing_interval, 
      dosing_interval = pk_params$dosing_interval
    )
  }
  
  # Calculate concentrations based on PK model
  if (pk_model == "one_compartment") {
    concentrations <- pk_one_compartment(
      time = time,
      dose = dose,
      clearance = pk_params$clearance,
      volume = pk_params$volume_central,
      dosing_times = dosing_schedule
    )
  } else if (pk_model == "two_compartment") {
    concentrations <- pk_two_compartment(
      time = time,
      dose = dose,
      clearance = pk_params$clearance,
      volume_central = pk_params$volume_central,
      volume_peripheral = pk_params$volume_peripheral,
      intercompartmental_clearance = pk_params$intercompartmental_clearance,
      dosing_times = dosing_schedule
    )
  } else {
    stop("Unknown PK model: ", pk_model)
  }
  
  return(concentrations)
}
