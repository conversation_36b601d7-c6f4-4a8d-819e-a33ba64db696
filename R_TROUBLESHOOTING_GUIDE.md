# R Script Troubleshooting Guide: QSP Thyroid Model

## Problem: R Script Runs Silently Without Output

This guide helps diagnose and fix issues with running the QSP thyroid model R scripts.

## Step 1: Check R Installation and Version

### Windows:
```powershell
# Check if R is installed and accessible
R --version

# Alternative check
where R
```

### Expected Output:
```
R version 4.0.0 or higher (2020-04-24) -- "Arbor Day"
```

**Minimum Requirements:**
- R version ≥ 4.0.0
- Rscript should be available in PATH

## Step 2: Required R Packages

The QSP model requires these packages:

### Core Dependencies (CRITICAL):
```r
install.packages(c(
  "R6",           # Object-oriented programming
  "deSolve",      # ODE solver
  "dplyr",        # Data manipulation
  "futile.logger" # Logging
))
```

### Additional Dependencies:
```r
install.packages(c(
  "ggplot2",      # Plotting
  "readr",        # File I/O
  "purrr",        # Functional programming
  "pracma",       # Numerical analysis (for trapz function)
  "scales"        # Scaling functions
))
```

### Optional Dependencies:
```r
install.packages(c(
  "pROC",         # ROC analysis (optional)
  "parallel"      # Parallel processing (optional)
))
```

## Step 3: Common Execution Methods

### Method 1: Command Line (Recommended)
```bash
# Windows Command Prompt
Rscript R/example_usage.R

# Windows PowerShell
Rscript.exe R/example_usage.R

# If Rscript not in PATH, use full path
"C:\Program Files\R\R-4.x.x\bin\Rscript.exe" R/example_usage.R
```

### Method 2: R Console
```r
# Start R console, then:
source("R/example_usage.R")
```

### Method 3: RStudio
1. Open RStudio
2. Set working directory: `setwd("path/to/QSP_PD_thyroid")`
3. Run: `source("R/example_usage.R")`

## Step 4: Diagnostic Script

Run this diagnostic script first to check your environment:

```r
# Save as: R/diagnostic_check.R
cat("=== QSP THYROID MODEL DIAGNOSTIC CHECK ===\n")

# Check R version
cat("R Version:", R.version.string, "\n")

# Check working directory
cat("Working Directory:", getwd(), "\n")

# Check if required files exist
required_files <- c(
  "R/qsp_model_core.R",
  "R/qsp_population_analysis.R",
  "R/example_usage.R"
)

cat("\nFile Check:\n")
for (file in required_files) {
  exists <- file.exists(file)
  cat("-", file, ":", ifelse(exists, "✓ EXISTS", "✗ MISSING"), "\n")
}

# Check required packages
required_packages <- c("R6", "deSolve", "dplyr", "futile.logger", "pracma")
cat("\nPackage Check:\n")
for (pkg in required_packages) {
  installed <- requireNamespace(pkg, quietly = TRUE)
  cat("-", pkg, ":", ifelse(installed, "✓ INSTALLED", "✗ MISSING"), "\n")
  if (!installed) {
    cat("  Install with: install.packages('", pkg, "')\n", sep = "")
  }
}

# Test basic functionality
cat("\nBasic Functionality Test:\n")
tryCatch({
  # Test R6 class creation
  if (requireNamespace("R6", quietly = TRUE)) {
    test_class <- R6::R6Class("Test", public = list(x = 1))
    cat("- R6 class creation: ✓ WORKING\n")
  }
  
  # Test deSolve
  if (requireNamespace("deSolve", quietly = TRUE)) {
    # Simple ODE test
    simple_ode <- function(t, y, parms) list(c(-y))
    sol <- deSolve::ode(y = 1, times = c(0, 1), func = simple_ode, parms = NULL)
    cat("- deSolve ODE solver: ✓ WORKING\n")
  }
  
}, error = function(e) {
  cat("- Error in basic functionality test:", e$message, "\n")
})

cat("\n=== DIAGNOSTIC COMPLETE ===\n")
```

## Step 5: Common Issues and Solutions

### Issue 1: "Rscript not found"
**Solution:**
- Add R to your system PATH
- Use full path to Rscript.exe
- Install R if not installed

### Issue 2: "Package not found" errors
**Solution:**
```r
# Install missing packages
install.packages(c("R6", "deSolve", "dplyr", "futile.logger", "pracma"))
```

### Issue 3: Script runs but no output
**Causes:**
- Script is running in non-interactive mode
- Output is being suppressed
- Errors are being caught silently

**Solution:**
```r
# Force interactive mode for testing
options(interactive = TRUE)
source("R/example_usage.R")
```

### Issue 4: "Object not found" errors
**Solution:**
- Ensure all source files are loaded
- Check file paths are correct
- Verify working directory

### Issue 5: ODE solver fails
**Solution:**
- Check deSolve package is installed
- Verify model parameters are valid
- Check for NaN/Inf values in initial conditions

## Step 6: Expected Output

When the script runs successfully, you should see:

```
================================================================================
QSP THYROID MODEL - COMPREHENSIVE USAGE EXAMPLES
================================================================================
Recalibrated parameters for realistic incidence:
- k_death = 0.003 day⁻¹(pg/mL)⁻¹ (reduced)
- EC50_IFN_death = 200 pg/mL (increased)
- epsilon = 0.15 pg cell⁻¹ day⁻¹ (reduced)
- Target incidence: 6-15% (drug-specific)

PART 1: SINGLE PATIENT SIMULATIONS
--------------------------------------------------

Simulating patient with Nivolumab...
  - Any hypothyroidism: Yes/No
  - Grade 2+ hypothyroidism: Yes/No
  - Time to onset: X.X days
  - Peak TSH: X.XX mIU/L
  - Max thyrocyte loss: X.X%
  - Plots saved to: example_outputs_R/single_patient_nivolumab

[Similar output for other drugs...]

PART 2: POPULATION SIMULATION (n=100)
--------------------------------------------------
Generating virtual patient cohort...

Cohort demographics:
- Age: XX.X ± X.X years
- Female: XX%
- HLA-DRB1*03+: XX%
- TPO-Ab+: XX%

Drug distribution:
- Nivolumab: XX patients
- Pembrolizumab: XX patients
- Atezolizumab: XX patients
- Durvalumab: XX patients

Running population simulation...

Overall incidence rates:
- Any hypothyroidism: XX.X%
- Grade 2+ hypothyroidism: XX.X%

Drug-specific incidence rates:
- Nivolumab: XX.X% any, XX.X% grade 2+
- Pembrolizumab: XX.X% any, XX.X% grade 2+
- Atezolizumab: XX.X% any, XX.X% grade 2+
- Durvalumab: XX.X% any, XX.X% grade 2+

[Additional sections...]

================================================================================
EXAMPLE USAGE COMPLETED SUCCESSFULLY!
================================================================================
```

## Step 7: Quick Test Script

Create and run this minimal test:

```r
# Save as: R/quick_test.R
cat("Starting QSP Model Quick Test...\n")

# Test 1: Load core model
tryCatch({
  source("R/qsp_model_core.R")
  cat("✓ Core model loaded successfully\n")
}, error = function(e) {
  cat("✗ Error loading core model:", e$message, "\n")
  stop("Cannot proceed without core model")
})

# Test 2: Create simple model
tryCatch({
  model <- QSPModel$new()
  cat("✓ QSP model created successfully\n")
}, error = function(e) {
  cat("✗ Error creating model:", e$message, "\n")
})

# Test 3: Run single simulation
tryCatch({
  result <- simulate_patient(model, t_span = c(0, 7), drug_type = 'nivolumab')
  cat("✓ Single patient simulation completed\n")
  cat("  - Simulation points:", nrow(result), "\n")
  cat("  - Final TSH:", round(tail(result$TSH, 1), 2), "mIU/L\n")
}, error = function(e) {
  cat("✗ Error in simulation:", e$message, "\n")
})

cat("Quick test completed!\n")
```

## Step 8: Getting Help

If issues persist:

1. **Check R console for error messages**
2. **Run the diagnostic script first**
3. **Try the quick test script**
4. **Check file permissions and paths**
5. **Verify R installation is complete**

## Contact Information

For additional support, provide:
- R version (`R.version.string`)
- Operating system
- Error messages (if any)
- Output from diagnostic script
