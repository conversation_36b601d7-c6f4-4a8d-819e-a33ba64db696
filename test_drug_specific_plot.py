#!/usr/bin/env python3
"""
Test script to verify drug-specific literature ranges in the population plot.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def create_test_data():
    """Create sample data for testing the drug-specific plot."""
    np.random.seed(42)
    
    # Create sample data for 4 drugs
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    n_patients_per_drug = 100
    
    data = []
    
    for drug in drugs:
        for i in range(n_patients_per_drug):
            # Simulate realistic incidence rates for each drug
            if drug == 'nivolumab':
                any_hypo = np.random.random() < 0.08  # 8% incidence
                grade2_hypo = any_hypo and (np.random.random() < 0.4)  # 40% of any hypo are grade2+
            elif drug == 'pembrolizumab':
                any_hypo = np.random.random() < 0.10  # 10% incidence
                grade2_hypo = any_hypo and (np.random.random() < 0.5)  # 50% of any hypo are grade2+
            elif drug == 'atezolizumab':
                any_hypo = np.random.random() < 0.06  # 6% incidence
                grade2_hypo = any_hypo and (np.random.random() < 0.35)  # 35% of any hypo are grade2+
            else:  # durvalumab
                any_hypo = np.random.random() < 0.05  # 5% incidence
                grade2_hypo = any_hypo and (np.random.random() < 0.3)  # 30% of any hypo are grade2+
            
            # Time to onset for those who develop hypothyroidism
            if any_hypo:
                time_to_onset = np.random.normal(60, 30)  # Mean 60 days, std 30
                time_to_onset = max(10, time_to_onset)  # Minimum 10 days
            else:
                time_to_onset = np.nan
            
            data.append({
                'patient_id': f'{drug}_{i:03d}',
                'drug_type': drug,
                'any_hypothyroidism': 1.0 if any_hypo else 0.0,
                'grade2_hypothyroidism': 1.0 if grade2_hypo else 0.0,
                'time_to_onset_days': time_to_onset
            })
    
    return pd.DataFrame(data)

def test_drug_specific_plot():
    """Test the drug-specific plotting functionality."""
    
    print("Testing Drug-Specific Literature Ranges Plot")
    print("=" * 50)
    
    # Create test data
    test_data = create_test_data()
    
    # Calculate and display incidence rates by drug
    print("\nTest Data Incidence Rates:")
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    
    for drug in drugs:
        drug_data = test_data[test_data['drug_type'] == drug]
        any_rate = (drug_data['any_hypothyroidism'].sum() / len(drug_data)) * 100
        grade2_rate = (drug_data['grade2_hypothyroidism'].sum() / len(drug_data)) * 100
        print(f"  {drug.capitalize()}: {any_rate:.1f}% any, {grade2_rate:.1f}% grade2+")
    
    # Test the plotting function from example_usage.py
    try:
        from example_usage import plot_population_results
        
        # Create output directory
        Path("test_outputs").mkdir(exist_ok=True)
        
        # Generate the plot
        plot_population_results(test_data, save_dir="test_outputs")
        
        print(f"\n✅ SUCCESS: Drug-specific plot generated successfully!")
        print(f"📊 Plot saved to: test_outputs/population_incidence_and_timing.png")
        print(f"\nThe plot should now show:")
        print(f"  - Drug-specific error bars for literature ranges")
        print(f"  - Nivolumab: 5-15% any, 2-8% grade2+")
        print(f"  - Pembrolizumab: 6-18% any, 3-10% grade2+")
        print(f"  - Atezolizumab: 5-12% any, 2-8% grade2+")
        print(f"  - Durvalumab: 3-10% any, 1-6% grade2+")
        
    except Exception as e:
        print(f"❌ ERROR: Failed to generate plot: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_drug_specific_plot()
    if success:
        print(f"\n🎉 Test completed successfully!")
    else:
        print(f"\n💥 Test failed!")
