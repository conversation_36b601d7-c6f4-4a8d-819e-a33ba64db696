---
title: "QSPThyroid: Getting Started with Quantitative Systems Pharmacology Modeling"
author: "QSP Modeling Team"
date: "`r Sys.Date()`"
output: 
  rmarkdown::html_vignette:
    toc: true
    toc_depth: 3
    fig_width: 7
    fig_height: 5
vignette: >
  %\VignetteIndexEntry{QSPThyroid: Getting Started with Quantitative Systems Pharmacology Modeling}
  %\VignetteEngine{knitr::rmarkdown}
  %\VignetteEncoding{UTF-8}
---

```{r setup, include = FALSE}
knitr::opts_chunk$set(
  collapse = TRUE,
  comment = "#>",
  fig.path = "figures/",
  warning = FALSE,
  message = FALSE
)

# Load required packages
library(QSPThyroid)
library(ggplot2)
library(dplyr)
```

## Introduction

QSPThyroid is a comprehensive R package for quantitative systems pharmacology (QSP) modeling of checkpoint-inhibitor-induced hypothyroidism. This vignette provides a step-by-step tutorial for using the package to:

1. Simulate individual patients
2. Analyze virtual patient populations
3. Perform risk stratification
4. Calibrate model parameters

## Model Overview

The QSP model integrates several key biological processes:

- **Checkpoint inhibitor pharmacokinetics**: Drug concentration over time
- **Immune activation**: T-effector cell dynamics and cytokine release
- **Thyroid damage**: IFN-γ-mediated thyrocyte death
- **Hormone regulation**: T3/TSH feedback control
- **Clinical outcomes**: Hypothyroidism severity classification

## Basic Usage

### Single Patient Simulation

Let's start with a simple single patient simulation:

```{r single-patient-basic}
# Create default model parameters
params <- ModelParameters$new()

# Create QSP model
model <- QSPModel$new(params)

# Simulate patient for 24 weeks (168 days)
result <- simulate_patient(model, t_span = c(0, 168), drug_type = 'nivolumab')

# View first few rows
head(result)
```

### Visualizing Results

```{r plot-basic-simulation, fig.cap="Basic simulation results showing TSH and T3 over time"}
# Create time-series plot
library(ggplot2)
library(tidyr)

# Prepare data for plotting
plot_data <- result %>%
  select(time, TSH, T3, IFN) %>%
  pivot_longer(cols = c(TSH, T3, IFN), names_to = "biomarker", values_to = "value")

# Create multi-panel plot
ggplot(plot_data, aes(x = time, y = value)) +
  geom_line(color = "blue", size = 1) +
  facet_wrap(~biomarker, scales = "free_y", ncol = 1) +
  labs(
    title = "QSP Model Simulation: Biomarker Dynamics",
    x = "Time (days)",
    y = "Concentration"
  ) +
  theme_minimal() +
  theme(strip.text = element_text(size = 12, face = "bold"))
```

### Patient-Specific Covariates

Now let's simulate a high-risk patient with specific characteristics:

```{r high-risk-patient}
# Create high-risk patient parameters
high_risk_params <- ModelParameters$new(
  sex_factor = 1.3,      # Female (30% higher risk)
  age_factor = 1.2,      # Age > 60 (20% higher risk)
  HLA_factor = 2.2,      # HLA-DRB1*03:01 positive (2.2x higher risk)
  TPO_Ab_titer = 2.0     # Elevated TPO antibodies (log10 scale)
)

# Create and simulate high-risk model
high_risk_model <- QSPModel$new(high_risk_params)
high_risk_result <- simulate_patient(high_risk_model, t_span = c(0, 168), drug_type = 'nivolumab')

# Calculate risk metrics
risk_metrics <- calculate_risk_score(high_risk_model, high_risk_result, time_horizon = 168)
print(risk_metrics)
```

### Comparing Different Drugs

Let's compare the effects of different checkpoint inhibitors:

```{r drug-comparison}
# Simulate same patient with different drugs
drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab')
drug_results <- list()

for (drug in drugs) {
  result <- simulate_patient(model, t_span = c(0, 168), drug_type = drug)
  result$drug <- drug
  drug_results[[drug]] <- result
}

# Combine results
combined_results <- do.call(rbind, drug_results)

# Plot TSH comparison
ggplot(combined_results, aes(x = time, y = TSH, color = drug)) +
  geom_line(size = 1) +
  labs(
    title = "TSH Response by Checkpoint Inhibitor",
    x = "Time (days)",
    y = "TSH (mIU/L)",
    color = "Drug"
  ) +
  theme_minimal() +
  scale_color_brewer(type = "qual", palette = "Set1")
```

## Population Analysis

### Generating Virtual Patient Cohorts

```{r virtual-cohort}
# Create population parameters
pop_params <- PopulationParameters$new(
  age_mean = 65.0,
  age_std = 10.0,
  female_proportion = 0.41,
  HLA_DRB1_03_prevalence = 0.15,
  TPO_Ab_positive_rate = 0.16
)

# Generate virtual cohort
cohort <- VirtualCohort$new(
  n_patients = 100,  # Small cohort for demonstration
  pop_params = pop_params,
  random_state = 42
)

# View patient characteristics
head(cohort$patients)
```

### Population Simulation

```{r population-simulation}
# Simulate all patients (this may take a few minutes)
# Using parallel = FALSE for vignette compatibility
population_results <- cohort$simulate_all(
  t_span = c(0, 168),
  save_timeseries = FALSE,
  parallel = FALSE
)

# View results summary
summary(population_results[c('grade2_hypothyroidism', 'grade3_hypothyroidism')])
```

### Risk Stratification

```{r risk-stratification}
# Create risk stratifier
stratifier <- RiskStratifier$new(population_results)

# Stratify patients into risk groups
stratification <- stratifier$stratify_patients()

# View risk group distribution
table(stratification$stratified_patients$risk_group)

# Plot risk distribution
ggplot(stratification$stratified_patients, aes(x = risk_group, fill = risk_group)) +
  geom_bar() +
  labs(
    title = "Risk Group Distribution",
    x = "Risk Group",
    y = "Number of Patients"
  ) +
  theme_minimal() +
  scale_fill_brewer(type = "seq", palette = "Reds") +
  theme(legend.position = "none")
```

### Risk Factor Analysis

```{r risk-factors}
# Analyze risk factors using logistic regression
risk_analysis <- stratifier$analyze_risk_factors(
  outcome = 'grade2_hypothyroidism',
  predictors = c('age', 'sex_factor', 'HLA_factor', 'TPO_Ab_log_titer')
)

# View odds ratios
risk_analysis$analysis[, c('predictor', 'odds_ratio', 'ci_lower', 'ci_upper', 'p_value')]
```

### Biomarker Performance

```{r biomarker-analysis}
# Analyze TPO antibody performance as biomarker
if ('TPO_Ab_log_titer' %in% names(population_results)) {
  biomarker_perf <- stratifier$biomarker_analysis('TPO_Ab_log_titer')
  
  # View performance metrics
  head(biomarker_perf$performance_metrics)
  
  # Plot ROC-like curve (sensitivity vs specificity)
  perf_data <- biomarker_perf$performance_metrics
  
  ggplot(perf_data, aes(x = 1 - specificity, y = sensitivity)) +
    geom_line(color = "blue", size = 1) +
    geom_point(color = "red", size = 2) +
    geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "gray") +
    labs(
      title = "TPO Antibody Biomarker Performance",
      x = "1 - Specificity (False Positive Rate)",
      y = "Sensitivity (True Positive Rate)"
    ) +
    theme_minimal() +
    coord_equal()
}
```

## Advanced Features

### Sensitivity Analysis

```{r sensitivity-analysis}
# Perform sensitivity analysis on key parameters
base_model <- QSPModel$new(ModelParameters$new())

sensitivity_params <- c('EC50_IFN_death', 'k_death', 'T_eff0', 'epsilon')

sensitivity_results <- sensitivity_analysis(
  base_model = base_model,
  parameters = sensitivity_params,
  perturbation_factor = 0.1,
  t_span = c(0, 168),
  drug_type = 'nivolumab'
)

# View sensitivity indices
print(sensitivity_results[, c('parameter', 'sensitivity_index')])

# Plot sensitivity indices
ggplot(sensitivity_results, aes(x = reorder(parameter, abs(sensitivity_index)), 
                               y = sensitivity_index)) +
  geom_col(fill = "steelblue") +
  coord_flip() +
  labs(
    title = "Parameter Sensitivity Analysis",
    x = "Parameter",
    y = "Sensitivity Index"
  ) +
  theme_minimal()
```

### Model Diagnostics

```{r model-diagnostics}
# Run model diagnostics
diagnostics <- model_diagnostics(model, result)

# View diagnostic summary
diagnostics$summary

# Check for any issues
if (!diagnostics$summary$overall_ok) {
  cat("Model diagnostics identified potential issues:\n")
  if (!diagnostics$summary$mass_balance_ok) cat("- Mass balance issues\n")
  if (!diagnostics$summary$stability_ok) cat("- Stability issues\n")
  if (!diagnostics$summary$plausibility_ok) cat("- Plausibility issues\n")
} else {
  cat("All diagnostic checks passed ✓\n")
}
```

## Command-Line Interface

The package also provides a command-line interface for batch processing:

```bash
# Single patient analysis
Rscript -e "QSPThyroid::run_qsp_analysis()" --mode single --drug-type nivolumab --patient-age 65 --patient-sex F

# Population analysis
Rscript -e "QSPThyroid::run_qsp_analysis()" --mode population --n-patients 1000 --parallel

# Comprehensive analysis with report
Rscript -e "QSPThyroid::run_qsp_analysis()" --mode all --output-dir results/ --report-format html
```

## Best Practices

### 1. Parameter Validation

Always validate your parameters before running simulations:

```{r parameter-validation}
# Check parameter ranges
params <- ModelParameters$new()

# Verify key parameters are within physiological ranges
stopifnot(params$EC50_IFN_death > 0 && params$EC50_IFN_death < 1000)
stopifnot(params$k_death > 0 && params$k_death < 1)
stopifnot(params$T_eff0 > 0 && params$T_eff0 < 10000)

cat("Parameter validation passed ✓\n")
```

### 2. Simulation Diagnostics

Always check simulation diagnostics:

```{r simulation-diagnostics}
# Run diagnostics after simulation
diagnostics <- model_diagnostics(model, result)

if (diagnostics$summary$overall_ok) {
  cat("Simulation diagnostics passed ✓\n")
} else {
  warning("Simulation diagnostics failed - check model parameters")
}
```

### 3. Reproducibility

Use random seeds for reproducible results:

```{r reproducibility}
# Set seed for reproducible population generation
set.seed(42)
cohort1 <- VirtualCohort$new(n_patients = 10, random_state = 42)

set.seed(42)
cohort2 <- VirtualCohort$new(n_patients = 10, random_state = 42)

# Verify identical results
identical(cohort1$patients, cohort2$patients)
```

## Conclusion

This vignette has demonstrated the core functionality of QSPThyroid:

- **Single patient simulation** with customizable parameters
- **Population analysis** with virtual patient cohorts
- **Risk stratification** and biomarker analysis
- **Advanced features** like sensitivity analysis and diagnostics

For more detailed information, see the package documentation and additional vignettes:

- `help(package="QSPThyroid")` - Package overview
- `?QSPModel` - Core model documentation
- `?VirtualCohort` - Population analysis
- `?RiskStratifier` - Risk assessment tools

## Session Information

```{r session-info}
sessionInfo()
```
