#!/usr/bin/env python3
"""
Debug T-cell dynamics to understand why T-cells are declining
"""

import numpy as np
from qsp_model_core import QSPModel

def debug_tcell_dynamics():
    """Debug T-cell dynamics in detail"""
    
    print("🔍 DEBUGGING T-CELL DYNAMICS")
    print("=" * 50)
    
    # Create model
    model = QSPModel(patient_id="DEBUG001", damage_susceptibility=2.0)
    
    # Test at early time with high T_eff
    t = 1.0
    drug_type = 'nivolumab'
    
    # State vector: [R, T_eff, IFN, Thyro, T3, TSH, cumulative_damage]
    y = np.array([0.0, 2000.0, 0.0, 1.0, 4.8, 1.5, 0.0])
    
    print(f"📊 INPUT STATE (t={t}):")
    for i, name in enumerate(model.state_names):
        print(f"  {name}: {y[i]:.6f}")
    
    # Get parameters
    p = model.params
    R, T_eff, <PERSON><PERSON>, Thyro, T3, TSH, cumulative_damage = y
    
    # Get drug concentration and checkpoint binding
    drug_conc = model.drug_concentration(t, drug_type)
    R_new = model.checkpoint_binding(drug_conc, drug_type)
    
    print(f"\n💊 DRUG ANALYSIS:")
    print(f"  Drug concentration: {drug_conc:,.0f} ng/mL")
    print(f"  Checkpoint binding (R_new): {R_new:.8f}")
    
    # Check activation
    drug_thresholds = {'nivolumab': 12000.0}
    activation_threshold = drug_thresholds[drug_type]
    condition = p.immune_susceptible and drug_conc >= activation_threshold
    print(f"  Activation condition: {condition}")
    
    # T-cell dynamics parameters
    APC_baseline = 1e6
    IL2_baseline = 0.1
    
    print(f"\n⚙️  T-CELL PARAMETERS:")
    print(f"  alpha: {p.alpha}")
    print(f"  beta: {p.beta}")
    print(f"  gamma: {p.gamma}")
    print(f"  delta: {p.delta}")
    print(f"  APC_baseline: {APC_baseline:,.0f}")
    print(f"  IL2_baseline: {IL2_baseline}")
    
    # Manual T-cell calculation
    if condition:
        activation_term = p.alpha * APC_baseline * (1 + p.gamma * R_new)
        death_term = p.beta * T_eff
        proliferation_term = p.delta * IL2_baseline * T_eff
        dT_eff_dt_manual = activation_term - death_term + proliferation_term
        
        print(f"\n✅ T-CELL DYNAMICS ACTIVE:")
        print(f"  Activation term: {activation_term:.3f}")
        print(f"  Death term: {death_term:.3f}")
        print(f"  Proliferation term: {proliferation_term:.3f}")
        print(f"  Net dT_eff_dt: {dT_eff_dt_manual:.3f}")
        
    else:
        dT_eff_dt_manual = -p.beta * T_eff
        print(f"\n❌ T-CELL DYNAMICS INACTIVE:")
        print(f"  Manual dT_eff_dt: {dT_eff_dt_manual:.3f}")
    
    # Call ODE system
    dy_dt = model.ode_system(t, y, drug_type)
    
    print(f"\n🚀 ODE SYSTEM OUTPUT:")
    print(f"  dT_eff/dt: {dy_dt[1]:.3f}")
    print(f"  dIFN/dt: {dy_dt[2]:.3f}")
    
    # Calculate steady-state T_eff
    if condition:
        # At steady state: dT_eff_dt = 0
        # activation_term - death_term + proliferation_term = 0
        # alpha * APC * (1 + gamma * R_new) = (beta - delta * IL2) * T_eff
        steady_state_T_eff = activation_term / (p.beta - p.delta * IL2_baseline)
        print(f"\n📈 STEADY STATE ANALYSIS:")
        print(f"  Steady-state T_eff: {steady_state_T_eff:.0f} cells/L")
        print(f"  Current T_eff: {T_eff:.0f} cells/L")
        print(f"  Ratio: {T_eff / steady_state_T_eff:.3f}")

if __name__ == "__main__":
    debug_tcell_dynamics()