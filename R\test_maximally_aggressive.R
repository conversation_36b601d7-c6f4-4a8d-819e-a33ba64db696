#!/usr/bin/env Rscript
#' QSP Thyroid Model - Test Maximally Aggressive Parameters
#' ========================================================
#'
#' This script tests the maximally aggressive parameter adjustments
#' to verify they produce realistic hypothyroidism incidence rates.
#'
#' Target: 10-20% any hypothyroidism, 5-15% grade 2+
#'
#' <AUTHOR> Modeling Team
#' @date 2024

# Clear R environment to ensure fresh start
rm(list = ls())
gc()

# Load required libraries
suppressMessages({
  library(dplyr)
  library(futile.logger)
})

cat("================================================================================\n")
cat("QSP THYROID MODEL - MAXIMALLY AGGRESSIVE PARAMETER TEST\n")
cat("================================================================================\n")
cat("Clearing R environment and reloading all components...\n\n")

# Force reload of all QSP model components
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")

# Set random seed for reproducibility
set.seed(42)

cat("STEP 1: Parameter Verification\n")
cat(rep("-", 50), "\n", sep = "")

# Create a test model to verify parameters
test_model <- QSPModel$new()
test_params <- test_model$params

cat("Verifying maximally aggressive parameters:\n")
cat("- k_death =", test_params$k_death, "(should be 0.0002)\n")
cat("- epsilon =", test_params$epsilon, "(should be 0.01)\n")
cat("- EC50_IFN_death =", test_params$EC50_IFN_death, "(should be 2000)\n")
cat("- k_clear_IFN =", test_params$k_clear_IFN, "(should be 50.0)\n")
cat("- cytokine_threshold_pg_ml =", test_params$cytokine_threshold_pg_ml, "(should be 5000.0)\n")

# Verify parameters are correct
param_check <- TRUE
if (test_params$k_death != 0.0002) {
  cat("❌ ERROR: k_death not updated correctly!\n")
  param_check <- FALSE
}
if (test_params$epsilon != 0.01) {
  cat("❌ ERROR: epsilon not updated correctly!\n")
  param_check <- FALSE
}
if (test_params$EC50_IFN_death != 2000) {
  cat("❌ ERROR: EC50_IFN_death not updated correctly!\n")
  param_check <- FALSE
}
if (test_params$cytokine_threshold_pg_ml != 5000.0) {
  cat("❌ ERROR: cytokine_threshold_pg_ml not updated correctly!\n")
  param_check <- FALSE
}

if (param_check) {
  cat("✅ All parameters verified correctly!\n\n")
} else {
  cat("❌ Parameter verification failed! Check qsp_model_core.R\n")
  stop("Parameters not updated correctly")
}

cat("STEP 2: Single Patient Test\n")
cat(rep("-", 50), "\n", sep = "")

# Test single patient simulation for each drug
drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
single_results <- list()

for (drug in drugs) {
  cat("Testing", drug, "...\n")
  
  # Create model
  model <- QSPModel$new()
  model$assign_immune_susceptibility(drug)
  model$assign_covariates()
  model$apply_covariate_effects()
  
  # Run simulation
  result <- model$simulate(
    drug_type = drug,
    t_span = c(0, 168),  # 24 weeks
    save_timeseries = FALSE
  )
  
  single_results[[drug]] <- result
  
  cat("- Final TSH:", round(result$final_TSH, 2), "mIU/L\n")
  cat("- Any hypothyroidism:", result$any_hypothyroidism, "\n")
  cat("- Grade 2+ hypothyroidism:", result$grade2_hypothyroidism, "\n")
}

cat("\nSTEP 3: Small Population Test (n=25)\n")
cat(rep("-", 50), "\n", sep = "")

# Create very small cohort for quick test
cohort_small <- VirtualCohort$new(n_patients = 25, random_state = 42)

# Run simulation
results_small <- cohort_small$simulate_all(
  t_span = c(0, 168),  # 24 weeks
  save_timeseries = FALSE,
  parallel = FALSE
)

# Calculate incidence rates
any_hypo_rate_small <- mean(results_small$any_hypothyroidism) * 100
grade2_hypo_rate_small <- mean(results_small$grade2_hypothyroidism) * 100

cat("Small population results (n=25):\n")
cat("- Any hypothyroidism:", round(any_hypo_rate_small, 1), "%\n")
cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_small, 1), "%\n")

# Drug-specific rates
drug_rates_small <- results_small %>%
  group_by(drug_type) %>%
  summarise(
    n = n(),
    any_rate = mean(any_hypothyroidism) * 100,
    grade2_rate = mean(grade2_hypothyroidism) * 100,
    .groups = 'drop'
  )

cat("Drug-specific rates (small cohort):\n")
for (i in 1:nrow(drug_rates_small)) {
  row <- drug_rates_small[i, ]
  cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
      round(row$any_rate, 1), "% any,", 
      round(row$grade2_rate, 1), "% grade 2+\n")
}

# Check if rates are reasonable
if (any_hypo_rate_small <= 40 && grade2_hypo_rate_small <= 30) {
  cat("✅ Small population test: PROMISING (rates significantly reduced)\n\n")
  
  # Proceed to medium population
  cat("STEP 4: Medium Population Test (n=100)\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Create medium cohort
  cohort_medium <- VirtualCohort$new(n_patients = 1000, random_state = 123)
  
  # Run simulation
  results_medium <- cohort_medium$simulate_all(
    t_span = c(0, 168),  # 24 weeks
    save_timeseries = FALSE,
    parallel = FALSE
  )
  
  # Calculate incidence rates
  any_hypo_rate_medium <- mean(results_medium$any_hypothyroidism) * 100
  grade2_hypo_rate_medium <- mean(results_medium$grade2_hypothyroidism) * 100
  
  cat("Medium population results (n=100):\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_medium, 1), "%\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_medium, 1), "%\n")
  
  # Drug-specific rates
  drug_rates_medium <- results_medium %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism) * 100,
      grade2_rate = mean(grade2_hypothyroidism) * 100,
      .groups = 'drop'
    )
  
  cat("Drug-specific rates (medium cohort):\n")
  for (i in 1:nrow(drug_rates_medium)) {
    row <- drug_rates_medium[i, ]
    cat("-", tools::toTitleCase(row$drug_type), "(n=", row$n, "):", 
        round(row$any_rate, 1), "% any,", 
        round(row$grade2_rate, 1), "% grade 2+\n")
  }
  
  # Final assessment
  cat("\nSTEP 5: Final Assessment\n")
  cat(rep("-", 50), "\n", sep = "")
  
  # Check if rates are in target range
  target_any_met <- any_hypo_rate_medium >= 10 && any_hypo_rate_medium <= 20
  target_grade2_met <- grade2_hypo_rate_medium >= 5 && grade2_hypo_rate_medium <= 15
  
  cat("Target validation:\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_medium, 1), "% (target: 10-20%) -", 
      ifelse(target_any_met, "PASS", "FAIL"), "\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_medium, 1), "% (target: 5-15%) -", 
      ifelse(target_grade2_met, "PASS", "FAIL"), "\n")
  
  if (target_any_met && target_grade2_met) {
    cat("\n🎉 SUCCESS: Maximally aggressive parameters achieved realistic rates!\n")
    cat("✅ Overall rates within target ranges\n")
    cat("✅ Significant improvement from previous 58%/52%\n")
    
    # Save successful results
    write.csv(results_medium, "maximally_aggressive_results.csv", row.names = FALSE)
    cat("✅ Results saved to: maximally_aggressive_results.csv\n")
    
    cat("\nRecommendation: Proceed with full example_usage.R\n")
    
  } else {
    if (any_hypo_rate_medium > 20) {
      cat("\n⚠ STILL TOO HIGH: Any hypothyroidism", round(any_hypo_rate_medium, 1), "% > 20%\n")
      cat("Further parameter reductions needed:\n")
      cat("- Consider epsilon = 0.005 (from 0.01)\n")
      cat("- Consider k_death = 0.0001 (from 0.0002)\n")
      cat("- Consider EC50_IFN_death = 5000 (from 2000)\n")
      cat("- Consider susceptibility rates = 0.001-0.01 (from 0.008-0.02)\n")
    } else if (any_hypo_rate_medium < 10) {
      cat("\n⚠ TOO LOW: Any hypothyroidism", round(any_hypo_rate_medium, 1), "% < 10%\n")
      cat("Parameters may be too aggressive. Consider slight increases.\n")
    }
    
    if (grade2_hypo_rate_medium > 15) {
      cat("- Grade 2+ hypothyroidism", round(grade2_hypo_rate_medium, 1), "% > 15%\n")
      cat("Consider further reducing k_death or increasing cytokine_threshold\n")
    } else if (grade2_hypo_rate_medium < 5) {
      cat("- Grade 2+ hypothyroidism", round(grade2_hypo_rate_medium, 1), "% < 5%\n")
      cat("Parameters may be too aggressive for grade 2+ events\n")
    }
  }
  
} else {
  cat("❌ Small population test: RATES STILL TOO HIGH\n")
  cat("- Any hypothyroidism:", round(any_hypo_rate_small, 1), "% (should be ≤40% for small sample)\n")
  cat("- Grade 2+ hypothyroidism:", round(grade2_hypo_rate_small, 1), "% (should be ≤30% for small sample)\n")
  cat("\n🚨 CRITICAL: Even maximally aggressive parameters insufficient!\n")
  cat("Possible issues:\n")
  cat("1. Parameters not being applied correctly\n")
  cat("2. Other model components overriding parameter effects\n")
  cat("3. Fundamental model structure issue\n")
  cat("4. R caching old parameter values\n")
  
  cat("\nDEBUGGING RECOMMENDATIONS:\n")
  cat("1. Restart R session completely\n")
  cat("2. Check if parameters are being overridden elsewhere\n")
  cat("3. Add debug prints in simulation functions\n")
  cat("4. Compare with Python implementation\n")
}

cat("\n", rep("=", 80), "\n", sep = "")
cat("MAXIMALLY AGGRESSIVE PARAMETER TEST COMPLETED\n")
cat(rep("=", 80), "\n", sep = "")
cat("Timestamp:", format(Sys.time()), "\n")

# Print final parameter summary for verification
cat("\nFINAL PARAMETER SUMMARY:\n")
cat("- k_death:", test_params$k_death, "\n")
cat("- epsilon:", test_params$epsilon, "\n")
cat("- EC50_IFN_death:", test_params$EC50_IFN_death, "\n")
cat("- cytokine_threshold_pg_ml:", test_params$cytokine_threshold_pg_ml, "\n")
