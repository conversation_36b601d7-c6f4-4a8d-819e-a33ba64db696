#!/usr/bin/env python3
"""
Debug the ODE system directly to see what's happening with cytokine production
"""

import numpy as np
from qsp_model_core import QSPModel

def debug_ode_system():
    """Debug the ODE system directly"""
    
    print("🔍 DEBUGGING ODE SYSTEM DIRECTLY")
    print("=" * 50)
    
    # Create model
    model = QSPModel(patient_id="DEBUG001", damage_susceptibility=2.0)
    
    # Test at day 7 with initial conditions but some T_eff
    t = 7.0
    drug_type = 'nivolumab'
    
    # State vector: [R, T_eff, IFN, Thyro, T3, TSH, cumulative_damage]
    y = np.array([0.0, 500.0, 0.0, 1.0, 4.8, 1.5, 0.0])
    
    print(f"📊 INPUT STATE (t={t}):")
    for i, name in enumerate(model.state_names):
        print(f"  {name}: {y[i]:.6f}")
    
    # Get parameters
    p = model.params
    print(f"\n⚙️  KEY PARAMETERS:")
    print(f"  immune_susceptible: {p.immune_susceptible}")
    print(f"  epsilon: {p.epsilon}")
    print(f"  k_clear_IFN: {p.k_clear_IFN}")
    
    # Manual step-by-step calculation
    R, T_eff, IFN, Thyro, T3, TSH, cumulative_damage = y
    
    # Get drug concentration
    drug_conc = model.drug_concentration(t, drug_type)
    print(f"\n💊 DRUG ANALYSIS:")
    print(f"  Drug concentration: {drug_conc:,.0f} ng/mL")
    
    # Check activation threshold
    drug_thresholds = {
        'nivolumab': 12000.0,
        'pembrolizumab': 10000.0, 
        'atezolizumab': 70000.0,
        'durvalumab': 5000.0
    }
    activation_threshold = drug_thresholds[drug_type]
    print(f"  Activation threshold: {activation_threshold:,.0f} ng/mL")
    print(f"  Above threshold: {drug_conc >= activation_threshold}")
    
    # Check condition
    condition = p.immune_susceptible and drug_conc >= activation_threshold
    print(f"  Final condition: {condition}")
    
    # Calculate cytokine dynamics manually
    if condition:
        amplification_factor = 1.0 + (IFN / (IFN + 50.0))
        base_production = p.epsilon * T_eff
        amplified_production = base_production * amplification_factor
        dIFN_dt_manual = amplified_production - p.k_clear_IFN * IFN
        print(f"\n✅ CYTOKINE PRODUCTION ACTIVE:")
        print(f"  Base production: {base_production:.3f}")
        print(f"  Amplification factor: {amplification_factor:.3f}")
        print(f"  Amplified production: {amplified_production:.3f}")
        print(f"  Clearance: {p.k_clear_IFN * IFN:.3f}")
        print(f"  Manual dIFN_dt: {dIFN_dt_manual:.3f}")
    else:
        dIFN_dt_manual = -p.k_clear_IFN * IFN
        print(f"\n❌ CYTOKINE PRODUCTION INACTIVE:")
        print(f"  Manual dIFN_dt: {dIFN_dt_manual:.3f}")
    
    # Call the actual ODE system
    print(f"\n🚀 CALLING ODE SYSTEM:")
    dy_dt = model.ode_system(t, y, drug_type)
    
    print(f"📈 ODE SYSTEM OUTPUT:")
    for i, name in enumerate(model.state_names):
        print(f"  d{name}/dt: {dy_dt[i]:.6f}")
    
    # Compare manual vs ODE system
    print(f"\n🔍 COMPARISON:")
    print(f"  Manual dIFN_dt: {dIFN_dt_manual:.6f}")
    print(f"  ODE dIFN_dt:    {dy_dt[2]:.6f}")
    print(f"  Match: {abs(dIFN_dt_manual - dy_dt[2]) < 1e-10}")

if __name__ == "__main__":
    debug_ode_system()