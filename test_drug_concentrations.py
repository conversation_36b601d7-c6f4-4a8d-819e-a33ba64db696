#!/usr/bin/env python3
"""
Test script to check actual drug concentrations vs activation thresholds
"""

import numpy as np
from qsp_model_core import QSPModel

def test_drug_concentrations():
    """Test actual drug concentrations achieved by different drugs"""
    
    model = QSPModel()
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    
    # Test at different time points
    time_points = [1, 3, 7, 14, 21, 28]  # days
    
    # Activation thresholds from the model
    drug_thresholds = {
        'nivolumab': 12000.0,
        'pembrolizumab': 10000.0, 
        'atezolizumab': 70000.0,
        'durvalumab': 5000.0
    }
    
    print("🧪 DRUG CONCENTRATION vs ACTIVATION THRESHOLD TEST")
    print("=" * 60)
    
    for drug in drugs:
        print(f"\n📊 {drug.upper()}")
        print("-" * 40)
        print(f"Activation threshold: {drug_thresholds[drug]:,.0f} ng/mL")
        
        max_conc = 0
        for t in time_points:
            conc = model.drug_concentration(t, drug)
            max_conc = max(max_conc, conc)
            print(f"  Day {t:2d}: {conc:8,.0f} ng/mL")
        
        print(f"  MAX:    {max_conc:8,.0f} ng/mL")
        threshold_ratio = max_conc / drug_thresholds[drug]
        print(f"  RATIO:  {threshold_ratio:8.3f} (>1.0 needed for activation)")
        
        if threshold_ratio >= 1.0:
            print(f"  STATUS: ✅ CAN ACTIVATE")
        else:
            print(f"  STATUS: ❌ CANNOT ACTIVATE (threshold too high)")

if __name__ == "__main__":
    test_drug_concentrations()