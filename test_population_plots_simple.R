#!/usr/bin/env Rscript
#' Simple test for population plotting functions
#' ===========================================

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(tidyr)
})

# Set random seed for reproducibility
set.seed(42)

# Create sample population results for testing
create_sample_data <- function(n_patients = 50) {
  
  # Sample drug types
  drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  
  # Create sample data
  sample_data <- data.frame(
    patient_id = sprintf('VP%04d', 1:n_patients),
    age = rnorm(n_patients, 65, 10),
    sex = sample(c('M', 'F'), n_patients, replace = TRUE),
    HLA_DRB1_03 = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    drug_type = sample(drugs, n_patients, replace = TRUE),
    any_hypothyroidism = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    grade2_hypothyroidism = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.90, 0.10)),
    time_to_onset_days = ifelse(sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)) == 1,
                               rnorm(n_patients, 84, 21), NA),
    peak_TSH_mIU_per_L = rlnorm(n_patients, 1.5, 0.5),
    max_thyrocyte_loss_percent = runif(n_patients, 0, 30),
    peak_drug_concentration = rnorm(n_patients, 100, 20),
    stringsAsFactors = FALSE
  )
  
  # Ensure realistic values
  sample_data$age <- pmax(pmin(sample_data$age, 90), 18)
  sample_data$peak_TSH_mIU_per_L <- pmax(sample_data$peak_TSH_mIU_per_L, 0.1)
  sample_data$max_thyrocyte_loss_percent <- pmax(pmin(sample_data$max_thyrocyte_loss_percent, 100), 0)
  sample_data$peak_drug_concentration <- pmax(sample_data$peak_drug_concentration, 0)
  
  return(sample_data)
}

# Define colors
COLORS <- list(
  tsh = "#2E86AB",
  t3 = "#A23B72", 
  drug = "#F18F01",
  cytokine = "#C73E1D",
  thyrocyte = "#4CAF50",
  grade1 = "#FFC107",
  grade2 = "#FF5722",
  normal = "#4CAF50"
)

# Simple theme
theme_simple <- function() {
  theme_minimal() +
    theme(
      text = element_text(size = 12),
      plot.title = element_text(size = 14, face = "bold", hjust = 0.5),
      axis.title = element_text(size = 12, face = "bold"),
      axis.text = element_text(size = 10)
    )
}

# Test incidence by drug function
test_incidence_plot <- function(data, save_path = NULL) {
  
  # Calculate incidence rates by drug
  drug_incidence <- data %>%
    group_by(drug_type) %>%
    summarise(
      n = n(),
      any_rate = mean(any_hypothyroidism, na.rm = TRUE) * 100,
      grade2_rate = mean(grade2_hypothyroidism, na.rm = TRUE) * 100,
      .groups = 'drop'
    )
  
  # Reshape for plotting
  plot_data <- drug_incidence %>%
    select(drug_type, any_rate, grade2_rate) %>%
    pivot_longer(
      cols = c(any_rate, grade2_rate),
      names_to = "severity",
      values_to = "incidence"
    ) %>%
    mutate(
      severity = factor(severity, levels = c("any_rate", "grade2_rate"),
                       labels = c("Any Hypothyroidism", "Grade 2+ Hypothyroidism"))
    )
  
  # Create plot
  p <- ggplot(plot_data, aes(x = drug_type, y = incidence, fill = severity)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    scale_fill_manual(values = c("Any Hypothyroidism" = COLORS$grade1, 
                                "Grade 2+ Hypothyroidism" = COLORS$grade2)) +
    labs(
      x = "Drug Type",
      y = "Incidence Rate (%)",
      title = "Hypothyroidism Incidence by Drug Type",
      fill = "Severity"
    ) +
    theme_simple() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 10, height = 6, dpi = 150)
  }
  
  return(p)
}

# Test onset distribution function
test_onset_plot <- function(data, save_path = NULL) {
  
  # Filter for cases with onset times
  onset_data <- data %>%
    filter(!is.na(time_to_onset_days) & time_to_onset_days > 0)
  
  if (nrow(onset_data) == 0) {
    cat("No onset time data available for plotting\n")
    return(NULL)
  }
  
  # Convert to weeks
  onset_data$onset_weeks <- onset_data$time_to_onset_days / 7
  
  # Create plot
  p <- ggplot(onset_data, aes(x = onset_weeks)) +
    geom_histogram(binwidth = 2, fill = COLORS$grade2, alpha = 0.7, color = "white") +
    labs(
      x = "Time to Onset (weeks)",
      y = "Number of Cases",
      title = "Distribution of Hypothyroidism Onset Times"
    ) +
    theme_simple()
  
  if (!is.null(save_path)) {
    ggsave(save_path, plot = p, width = 10, height = 6, dpi = 150)
  }
  
  return(p)
}

# Main test function
main <- function() {
  
  cat("Simple Population Plotting Test\n")
  cat(rep("=", 40), "\n", sep = "")
  
  # Create output directory
  output_dir <- "test_plots_simple"
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # Create sample data
  cat("Creating sample data...\n")
  sample_data <- create_sample_data(50)
  
  # Test plotting functions
  cat("Testing plotting functions...\n")
  
  tryCatch({
    # Test incidence plot
    cat("- Testing incidence plot...\n")
    p1 <- test_incidence_plot(sample_data, 
                              save_path = file.path(output_dir, "test_incidence.png"))
    cat("  ✓ Incidence plot created\n")
    
    # Test onset plot
    cat("- Testing onset distribution plot...\n")
    p2 <- test_onset_plot(sample_data, 
                          save_path = file.path(output_dir, "test_onset.png"))
    cat("  ✓ Onset distribution plot created\n")
    
    cat("\nAll tests completed successfully!\n")
    cat("Plots saved to:", output_dir, "\n")
    
  }, error = function(e) {
    cat("Error during testing:", e$message, "\n")
  })
  
  # Save sample data
  write.csv(sample_data, file.path(output_dir, "sample_data.csv"), row.names = FALSE)
  cat("Sample data saved for inspection\n")
  
  cat("\nTest completed!\n")
}

# Run main function
main() 