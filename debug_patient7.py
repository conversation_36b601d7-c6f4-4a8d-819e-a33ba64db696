from qsp_model_core import QSPModel, simulate_patient, ModelParameters
import pandas as pd

# Recreate Patient 7 scenario - non-susceptible nivolumab patient who developed hypothyroidism
print('Debugging non-susceptible patient developing hypothyroidism:')
print('='*60)

# Create a non-susceptible patient
params = ModelParameters()
model = QSPModel(params)

# Force non-susceptible assignment
model.params.immune_susceptible = False
model.params.susceptibility_level = "none"
model.params.susceptibility_assigned = True
model._apply_covariates()

print(f'Patient parameters:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_level: {model.params.susceptibility_level}')
print(f'  k_death: {model.params.k_death}')
print(f'  epsilon: {model.params.epsilon}')

# Simulate with nivolumab
drug_type = 'nivolumab'
results = simulate_patient(model, t_span=(0, 168), drug_type=drug_type)

# Check key timepoints
print(f'\nSimulation results:')
print(f'  Final TSH: {results.iloc[-1]["TSH"]:.2f} mIU/L')
print(f'  Final IFN: {results.iloc[-1]["IFN"]:.1f} pg/mL')
print(f'  Final Thyro: {results.iloc[-1]["Thyro"]:.3f}')
print(f'  Final Drug: {results.iloc[-1]["Drug"]:.1f} ng/mL')

# Check if immune activation occurred
max_IFN = results['IFN'].max()
print(f'  Max IFN: {max_IFN:.1f} pg/mL')

if max_IFN > 0.1:
    print(f'  ERROR: Non-susceptible patient produced IFN-γ!')
    
    # Check drug concentration vs threshold
    drug_thresholds = {
        'nivolumab': 45000.0,
        'pembrolizumab': 30000.0,
        'atezolizumab': 200000.0,
        'durvalumab': 150000.0
    }
    
    threshold = drug_thresholds[drug_type]
    max_drug = results['Drug'].max()
    print(f'  Drug threshold: {threshold:.0f} ng/mL')
    print(f'  Max drug concentration: {max_drug:.0f} ng/mL')
    print(f'  Above threshold: {max_drug >= threshold}')
    
    # Check activation logic
    print(f'\nActivation logic check:')
    print(f'  immune_susceptible AND drug >= threshold: {model.params.immune_susceptible} AND {max_drug >= threshold} = {model.params.immune_susceptible and max_drug >= threshold}')
    
    if not model.params.immune_susceptible:
        print(f'  CONCLUSION: Non-susceptible patient should NOT activate immune response!')
        print(f'  BUG: The activation logic is not working correctly.')
else:
    print(f'  Good: Non-susceptible patient did not produce IFN-γ')

# Check if there's another mechanism causing hypothyroidism
thyro_loss = (1 - results.iloc[-1]["Thyro"]) * 100
print(f'  Thyrocyte loss: {thyro_loss:.1f}%')

if thyro_loss > 0.1:
    print(f'  ERROR: Thyrocyte loss occurred without IFN-γ!')
    print(f'  This suggests a bug in the ODE system.')
