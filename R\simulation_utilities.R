#' @title Simulation Utilities
#' @description Advanced simulation utilities for the QSP model including
#' parallel processing, sensitivity analysis, and model diagnostics.
#' 
#' <AUTHOR> Modeling Team
#' @docType package
#' @name simulation_utilities
NULL

#' @title Parallel Patient Simulation
#' @description Simulate multiple patients in parallel using cross-platform compatible
#' parallel processing (Windows, macOS, Linux). Uses the future framework for 
#' robust parallel execution across different operating systems.
#' @param models List of QSPModel objects
#' @param t_span Time span for simulation
#' @param drug_types Vector of drug types for each patient
#' @param n_cores Number of cores for parallel processing
#' @param rtol Relative tolerance for ODE solver
#' @param atol Absolute tolerance for ODE solver
#' @return List of simulation results
#' @export
simulate_patients_parallel <- function(models, t_span = c(0, 168), 
                                      drug_types = NULL, n_cores = NULL,
                                      rtol = 1e-6, atol = 1e-9) {
  
  if (is.null(n_cores)) {
    # Safely detect cores with fallback for Windows compatibility
    tryCatch({
      detected_cores <- parallel::detectCores()
      if (is.na(detected_cores) || detected_cores < 1) {
        n_cores <- 1
        futile.logger::flog.warn("Could not detect cores, using single core")
      } else {
        n_cores <- min(detected_cores - 1, length(models))
      }
    }, error = function(e) {
      n_cores <- 1
      futile.logger::flog.warn("Core detection failed, using single core: %s", e$message)
    })
  }
  
  if (is.null(drug_types)) {
    drug_types <- rep('nivolumab', length(models))
  }
  
  futile.logger::flog.info("Starting parallel simulation of %d patients using %d cores", 
                          length(models), n_cores)
  
  # Create simulation function
  sim_func <- function(i) {
    tryCatch({
      simulate_patient(
        model = models[[i]], 
        t_span = t_span, 
        drug_type = drug_types[i],
        rtol = rtol, 
        atol = atol
      )
    }, error = function(e) {
      futile.logger::flog.warn("Simulation failed for patient %d: %s", i, e$message)
      return(NULL)
    })
  }
  
  # Run parallel simulation (Windows compatible)
  if (n_cores > 1) {
    # Use future framework for cross-platform parallel processing
    if (!requireNamespace("future", quietly = TRUE) || !requireNamespace("future.apply", quietly = TRUE)) {
      futile.logger::flog.warn("future or future.apply not available, falling back to sequential processing")
      results <- lapply(seq_along(models), sim_func)
    } else {
      # Set up parallel plan for Windows compatibility
      old_plan <- future::plan()
      on.exit(future::plan(old_plan), add = TRUE)
      
      # Use multisession for Windows compatibility
      future::plan(future::multisession, workers = n_cores)
      results <- future.apply::future_lapply(seq_along(models), sim_func, 
                                           future.seed = TRUE)
    }
  } else {
    results <- lapply(seq_along(models), sim_func)
  }
  
  # Filter out failed simulations
  successful_results <- results[!sapply(results, is.null)]
  
  futile.logger::flog.info("Completed %d/%d successful simulations", 
                          length(successful_results), length(models))
  
  return(successful_results)
}

#' @title Sensitivity Analysis
#' @description Perform local sensitivity analysis on model parameters
#' @param base_model QSPModel object with baseline parameters
#' @param parameters Vector of parameter names to analyze
#' @param perturbation_factor Relative perturbation (e.g., 0.1 for ±10%)
#' @param t_span Time span for simulation
#' @param drug_type Drug type for simulation
#' @param endpoint Endpoint to analyze (function of simulation results)
#' @return Data frame with sensitivity indices
#' @export
sensitivity_analysis <- function(base_model, parameters, perturbation_factor = 0.1,
                                t_span = c(0, 168), drug_type = 'nivolumab',
                                endpoint = function(x) max(x$TSH)) {
  
  futile.logger::flog.info("Starting sensitivity analysis for %d parameters", length(parameters))
  
  # Baseline simulation
  baseline_result <- simulate_patient(base_model, t_span, drug_type)
  baseline_endpoint <- endpoint(baseline_result)
  
  sensitivity_results <- data.frame(
    parameter = character(0),
    baseline_value = numeric(0),
    perturbed_value_low = numeric(0),
    perturbed_value_high = numeric(0),
    endpoint_low = numeric(0),
    endpoint_high = numeric(0),
    sensitivity_index = numeric(0),
    stringsAsFactors = FALSE
  )
  
  for (param in parameters) {
    tryCatch({
      # Get baseline parameter value
      baseline_value <- base_model$params[[param]]
      
      # Create perturbed models
      model_low <- base_model$clone(deep = TRUE)
      model_high <- base_model$clone(deep = TRUE)
      
      perturbed_low <- baseline_value * (1 - perturbation_factor)
      perturbed_high <- baseline_value * (1 + perturbation_factor)
      
      model_low$params[[param]] <- perturbed_low
      model_high$params[[param]] <- perturbed_high
      
      # Simulate perturbed models
      result_low <- simulate_patient(model_low, t_span, drug_type)
      result_high <- simulate_patient(model_high, t_span, drug_type)
      
      endpoint_low <- endpoint(result_low)
      endpoint_high <- endpoint(result_high)
      
      # Calculate sensitivity index
      sensitivity_index <- (endpoint_high - endpoint_low) / (2 * perturbation_factor * baseline_endpoint)
      
      # Store results
      sensitivity_results <- rbind(sensitivity_results, data.frame(
        parameter = param,
        baseline_value = baseline_value,
        perturbed_value_low = perturbed_low,
        perturbed_value_high = perturbed_high,
        endpoint_low = endpoint_low,
        endpoint_high = endpoint_high,
        sensitivity_index = sensitivity_index,
        stringsAsFactors = FALSE
      ))
      
    }, error = function(e) {
      futile.logger::flog.warn("Sensitivity analysis failed for parameter %s: %s", param, e$message)
    })
  }
  
  # Sort by absolute sensitivity index
  sensitivity_results <- sensitivity_results[order(abs(sensitivity_results$sensitivity_index), decreasing = TRUE), ]
  
  futile.logger::flog.info("Sensitivity analysis completed for %d parameters", nrow(sensitivity_results))
  
  return(sensitivity_results)
}

#' @title Model Diagnostics
#' @description Perform model diagnostics including mass balance and stability checks
#' @param model QSPModel object
#' @param simulation_result Output from simulate_patient
#' @return List with diagnostic metrics
#' @export
model_diagnostics <- function(model, simulation_result) {
  
  diagnostics <- list()
  
  # Mass balance checks
  diagnostics$mass_balance <- list()
  
  # Check if thyrocyte mass stays within bounds
  thyro_min <- min(simulation_result$Thyro)
  thyro_max <- max(simulation_result$Thyro)
  diagnostics$mass_balance$thyrocyte_bounds <- list(
    min = thyro_min,
    max = thyro_max,
    within_bounds = thyro_min >= 0 && thyro_max <= model$params$Thyro_max * 1.1
  )
  
  # Check for negative concentrations
  negative_checks <- list(
    T_eff = any(simulation_result$T_eff < 0),
    IFN = any(simulation_result$IFN < 0),
    T3 = any(simulation_result$T3 < 0),
    TSH = any(simulation_result$TSH < 0)
  )
  diagnostics$mass_balance$negative_concentrations <- negative_checks
  
  # Stability checks
  diagnostics$stability <- list()
  
  # Check for oscillations (high frequency changes)
  for (var in c('T_eff', 'IFN', 'Thyro', 'T3', 'TSH')) {
    values <- simulation_result[[var]]
    if (length(values) > 3) {
      # Calculate second derivative as measure of oscillation
      second_deriv <- diff(diff(values))
      oscillation_metric <- mean(abs(second_deriv))
      diagnostics$stability[[paste0(var, "_oscillation")]] <- oscillation_metric
    }
  }
  
  # Check for steady state approach
  final_window <- tail(simulation_result, 20)  # Last 20 time points
  for (var in c('T_eff', 'IFN', 'Thyro', 'T3', 'TSH')) {
    values <- final_window[[var]]
    cv <- sd(values) / mean(values)  # Coefficient of variation
    diagnostics$stability[[paste0(var, "_steady_state_cv")]] <- cv
  }
  
  # Physiological plausibility checks
  diagnostics$plausibility <- list()
  
  # TSH range check (should be 0.1-100 mIU/L in pathological states)
  TSH_range <- range(simulation_result$TSH)
  diagnostics$plausibility$TSH_range <- list(
    min = TSH_range[1],
    max = TSH_range[2],
    plausible = TSH_range[1] >= 0.01 && TSH_range[2] <= 200
  )
  
  # T3 range check (should be 1-10 pmol/L in pathological states)
  T3_range <- range(simulation_result$T3)
  diagnostics$plausibility$T3_range <- list(
    min = T3_range[1],
    max = T3_range[2],
    plausible = T3_range[1] >= 0.5 && T3_range[2] <= 15
  )
  
  # IFN-γ range check (should be 0-1000 pg/mL)
  IFN_range <- range(simulation_result$IFN)
  diagnostics$plausibility$IFN_range <- list(
    min = IFN_range[1],
    max = IFN_range[2],
    plausible = IFN_range[1] >= 0 && IFN_range[2] <= 2000
  )
  
  # Overall diagnostic summary
  diagnostics$summary <- list(
    mass_balance_ok = all(!unlist(negative_checks)) && diagnostics$mass_balance$thyrocyte_bounds$within_bounds,
    stability_ok = all(sapply(diagnostics$stability, function(x) x < 1.0)),  # Arbitrary threshold
    plausibility_ok = all(sapply(diagnostics$plausibility, function(x) x$plausible))
  )
  
  diagnostics$summary$overall_ok <- all(unlist(diagnostics$summary[1:3]))
  
  return(diagnostics)
}

#' @title Batch Simulation with Error Handling
#' @description Simulate multiple scenarios with robust error handling
#' @param scenarios List of simulation scenarios (each with model, t_span, drug_type)
#' @param max_retries Maximum number of retries for failed simulations
#' @param rtol Relative tolerance for ODE solver
#' @param atol Absolute tolerance for ODE solver
#' @return List with successful results and error summary
#' @export
batch_simulation <- function(scenarios, max_retries = 2, rtol = 1e-6, atol = 1e-9) {
  
  futile.logger::flog.info("Starting batch simulation of %d scenarios", length(scenarios))
  
  results <- list()
  errors <- list()
  
  for (i in seq_along(scenarios)) {
    scenario <- scenarios[[i]]
    success <- FALSE
    retry_count <- 0
    
    while (!success && retry_count <= max_retries) {
      tryCatch({
        result <- simulate_patient(
          model = scenario$model,
          t_span = scenario$t_span %||% c(0, 168),
          drug_type = scenario$drug_type %||% 'nivolumab',
          rtol = rtol,
          atol = atol
        )
        
        # Run diagnostics
        diagnostics <- model_diagnostics(scenario$model, result)
        
        if (diagnostics$summary$overall_ok) {
          results[[i]] <- list(
            scenario_id = i,
            simulation = result,
            diagnostics = diagnostics
          )
          success <- TRUE
        } else {
          stop("Diagnostic checks failed")
        }
        
      }, error = function(e) {
        retry_count <<- retry_count + 1
        if (retry_count > max_retries) {
          errors[[i]] <- list(
            scenario_id = i,
            error_message = e$message,
            retries = retry_count - 1
          )
          futile.logger::flog.warn("Scenario %d failed after %d retries: %s", 
                                  i, retry_count - 1, e$message)
        }
      })
    }
  }
  
  futile.logger::flog.info("Batch simulation completed: %d successful, %d failed", 
                          length(results), length(errors))
  
  return(list(
    results = results,
    errors = errors,
    summary = list(
      total_scenarios = length(scenarios),
      successful = length(results),
      failed = length(errors),
      success_rate = length(results) / length(scenarios)
    )
  ))
}

# Helper function for null coalescing
`%||%` <- function(x, y) if (is.null(x)) y else x
