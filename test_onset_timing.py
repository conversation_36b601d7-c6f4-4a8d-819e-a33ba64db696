#!/usr/bin/env python3
"""
Test script to verify the time to onset fix for hypothyroidism.
This script analyzes the distribution of onset times to ensure they are realistic.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def analyze_onset_timing():
    """Analyze the time to onset distribution from the population results."""
    
    # Load the population results
    df = pd.read_csv('example_outputs/population_results.csv')
    
    # Filter patients who developed hypothyroidism
    hypo_patients = df[df['any_hypothyroidism'] == 1.0]
    grade2_patients = df[df['grade2_hypothyroidism'] == 1.0]
    
    print("=" * 60)
    print("TIME TO ONSET ANALYSIS - HYPOTHYROIDISM FIX VERIFICATION")
    print("=" * 60)
    
    print(f"\nTotal patients: {len(df)}")
    print(f"Patients with any hypothyroidism: {len(hypo_patients)} ({len(hypo_patients)/len(df)*100:.1f}%)")
    print(f"Patients with grade 2+ hypothyroidism: {len(grade2_patients)} ({len(grade2_patients)/len(df)*100:.1f}%)")
    
    # Analyze time to onset for grade 2+ patients
    onset_times = grade2_patients['time_to_onset_days'].dropna()
    
    if len(onset_times) > 0:
        print(f"\nTime to onset analysis (Grade 2+ patients):")
        print(f"Number of patients with onset data: {len(onset_times)}")
        print(f"Mean time to onset: {onset_times.mean():.1f} days ({onset_times.mean()/30:.1f} months)")
        print(f"Median time to onset: {onset_times.median():.1f} days ({onset_times.median()/30:.1f} months)")
        print(f"Min time to onset: {onset_times.min():.1f} days ({onset_times.min()/30:.1f} months)")
        print(f"Max time to onset: {onset_times.max():.1f} days ({onset_times.max()/30:.1f} months)")
        print(f"Standard deviation: {onset_times.std():.1f} days")
        
        # Categorize onset times
        early_onset = onset_times[onset_times <= 14]  # ≤2 weeks
        moderate_onset = onset_times[(onset_times > 14) & (onset_times <= 90)]  # 2 weeks - 3 months
        late_onset = onset_times[onset_times > 90]  # >3 months
        
        print(f"\nOnset timing distribution:")
        print(f"Early onset (≤2 weeks): {len(early_onset)} patients ({len(early_onset)/len(onset_times)*100:.1f}%)")
        print(f"Moderate onset (2 weeks - 3 months): {len(moderate_onset)} patients ({len(moderate_onset)/len(onset_times)*100:.1f}%)")
        print(f"Late onset (>3 months): {len(late_onset)} patients ({len(late_onset)/len(onset_times)*100:.1f}%)")
        
        # Show specific examples
        print(f"\nSpecific onset time examples:")
        for i, (idx, row) in enumerate(grade2_patients.head(10).iterrows()):
            if not pd.isna(row['time_to_onset_days']):
                print(f"  {row['patient_id']}: {row['time_to_onset_days']:.1f} days ({row['time_to_onset_days']/30:.1f} months) - {row['drug_type']} - {row['susceptibility_level']}")
        
        # Create a histogram
        plt.figure(figsize=(10, 6))
        plt.hist(onset_times, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(onset_times.mean(), color='red', linestyle='--', label=f'Mean: {onset_times.mean():.1f} days')
        plt.axvline(onset_times.median(), color='orange', linestyle='--', label=f'Median: {onset_times.median():.1f} days')
        plt.xlabel('Time to Onset (days)')
        plt.ylabel('Number of Patients')
        plt.title('Distribution of Time to Onset of Grade 2+ Hypothyroidism')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('example_outputs/onset_timing_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"\nHistogram saved to: example_outputs/onset_timing_distribution.png")
        
    else:
        print("\nNo patients with grade 2+ hypothyroidism found.")
    
    # Drug-specific analysis
    print(f"\nDrug-specific analysis:")
    for drug in df['drug_type'].unique():
        drug_data = df[df['drug_type'] == drug]
        drug_hypo = drug_data[drug_data['grade2_hypothyroidism'] == 1.0]
        
        if len(drug_hypo) > 0:
            drug_onset_times = drug_hypo['time_to_onset_days'].dropna()
            if len(drug_onset_times) > 0:
                print(f"  {drug.capitalize()}: {len(drug_hypo)} patients, mean onset: {drug_onset_times.mean():.1f} days")
    
    print(f"\n" + "=" * 60)
    print("ANALYSIS COMPLETE")
    print("=" * 60)
    
    # Summary assessment
    if len(onset_times) > 0:
        mean_months = onset_times.mean() / 30
        if mean_months >= 1.0:
            print(f"✓ SUCCESS: Mean time to onset is {mean_months:.1f} months (realistic)")
        else:
            print(f"⚠ WARNING: Mean time to onset is {mean_months:.1f} months (still too quick)")
        
        if len(early_onset) / len(onset_times) <= 0.3:
            print(f"✓ SUCCESS: Only {len(early_onset)/len(onset_times)*100:.1f}% of patients have early onset (≤2 weeks)")
        else:
            print(f"⚠ WARNING: {len(early_onset)/len(onset_times)*100:.1f}% of patients have early onset (too many)")

if __name__ == "__main__":
    analyze_onset_timing() 