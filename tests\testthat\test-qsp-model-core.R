#' @title Test QSP Model Core Components
#' @description Unit tests for core QSP model functionality with numerical tolerance checks

# Test tolerance for numerical comparisons
NUMERICAL_TOLERANCE <- 1e-6

# Reference values from Python implementation (these would be loaded from reference data)
REFERENCE_VALUES <- list(
  # Parameter values
  parameters = list(
    EC50_IFN_death = 100.0,
    k_death = 0.05,
    T_eff0 = 1000.0,
    epsilon = 2.0,
    k_clear_IFN = 2.0,
    alpha = 1e-3,
    beta = 0.2,
    k_regen = 0.05,
    Thyro_max = 1.0,
    TSH_baseline = 1.5,
    T3_baseline = 4.0
  ),
  
  # Simulation results for standard patient
  simulation = list(
    final_TSH = 2.1,
    final_T3 = 3.8,
    final_IFN = 45.2,
    final_Thyro = 0.95,
    max_TSH = 3.2,
    time_to_grade2 = 84.5
  )
)

test_that("ModelParameters class initialization works correctly", {
  # Test default initialization
  params <- ModelParameters$new()
  
  expect_s3_class(params, "ModelParameters")
  expect_s3_class(params, "R6")
  
  # Test parameter values match expected defaults
  expect_equal(params$EC50_IFN_death, REFERENCE_VALUES$parameters$EC50_IFN_death, 
               tolerance = NUMERICAL_TOLERANCE)
  expect_equal(params$k_death, REFERENCE_VALUES$parameters$k_death, 
               tolerance = NUMERICAL_TOLERANCE)
  expect_equal(params$T_eff0, REFERENCE_VALUES$parameters$T_eff0, 
               tolerance = NUMERICAL_TOLERANCE)
  
  # Test parameter bounds
  expect_true(params$EC50_IFN_death > 0)
  expect_true(params$k_death > 0)
  expect_true(params$T_eff0 > 0)
  expect_true(params$Thyro_max > 0)
})

test_that("ModelParameters covariate effects work correctly", {
  # Test sex factor
  params_male <- ModelParameters$new(sex_factor = 1.0)
  params_female <- ModelParameters$new(sex_factor = 1.3)
  
  expect_equal(params_male$sex_factor, 1.0)
  expect_equal(params_female$sex_factor, 1.3)
  
  # Test age factor
  params_young <- ModelParameters$new(age_factor = 1.0)
  params_old <- ModelParameters$new(age_factor = 1.2)
  
  expect_equal(params_young$age_factor, 1.0)
  expect_equal(params_old$age_factor, 1.2)
  
  # Test HLA factor
  params_hla_neg <- ModelParameters$new(HLA_factor = 1.0)
  params_hla_pos <- ModelParameters$new(HLA_factor = 2.2)
  
  expect_equal(params_hla_neg$HLA_factor, 1.0)
  expect_equal(params_hla_pos$HLA_factor, 2.2)
})

test_that("QSPModel class initialization works correctly", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  expect_s3_class(model, "QSPModel")
  expect_s3_class(model, "R6")
  expect_identical(model$params, params)
})

test_that("Drug concentration calculations are correct", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Test nivolumab concentration
  conc_nivo <- model$drug_concentration(time = 7, drug_type = 'nivolumab')
  expect_true(is.numeric(conc_nivo))
  expect_true(conc_nivo >= 0)
  expect_true(length(conc_nivo) == 1)
  
  # Test pembrolizumab concentration
  conc_pembro <- model$drug_concentration(time = 7, drug_type = 'pembrolizumab')
  expect_true(is.numeric(conc_pembro))
  expect_true(conc_pembro >= 0)
  
  # Test atezolizumab concentration
  conc_atezo <- model$drug_concentration(time = 7, drug_type = 'atezolizumab')
  expect_true(is.numeric(conc_atezo))
  expect_true(conc_atezo >= 0)
  
  # Test that concentrations decrease over time
  conc_early <- model$drug_concentration(time = 1, drug_type = 'nivolumab')
  conc_late <- model$drug_concentration(time = 14, drug_type = 'nivolumab')
  expect_true(conc_early > conc_late)
})

test_that("Checkpoint binding calculations are correct", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Test binding calculation
  drug_conc <- 1000  # ng/mL
  binding <- model$checkpoint_binding(drug_conc)
  
  expect_true(is.numeric(binding))
  expect_true(binding >= 0)
  expect_true(binding <= 1)  # Should be a fraction
  
  # Test that higher drug concentration leads to higher binding
  binding_low <- model$checkpoint_binding(100)
  binding_high <- model$checkpoint_binding(10000)
  expect_true(binding_high > binding_low)
  
  # Test saturation behavior
  binding_very_high <- model$checkpoint_binding(1e6)
  expect_true(binding_very_high < 1.0)  # Should not exceed 100%
})

test_that("ODE system function works correctly", {
  params <- ModelParameters$new()
  
  # Test initial conditions
  y0 <- initial_conditions(params)
  expect_true(is.numeric(y0))
  expect_equal(length(y0), 6)  # 6 state variables
  expect_true(all(y0 >= 0))  # All initial values should be non-negative
  
  # Test ODE system evaluation
  time <- 0
  state <- y0
  parms <- list(params = params, drug_type = 'nivolumab')
  
  dydt <- ode_system(time, state, parms)
  
  expect_true(is.list(dydt))
  expect_equal(length(dydt[[1]]), 6)  # 6 derivatives
  expect_true(all(is.finite(dydt[[1]])))  # All derivatives should be finite
})

test_that("Patient simulation produces valid results", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Test simulation
  result <- simulate_patient(model, t_span = c(0, 168), drug_type = 'nivolumab')
  
  expect_true(is.data.frame(result))
  expect_true(nrow(result) > 0)
  
  # Check required columns
  required_cols <- c('time', 'R', 'T_eff', 'IFN', 'Thyro', 'T3', 'TSH')
  expect_true(all(required_cols %in% names(result)))
  
  # Check value ranges
  expect_true(all(result$time >= 0))
  expect_true(all(result$R >= 0))
  expect_true(all(result$T_eff >= 0))
  expect_true(all(result$IFN >= 0))
  expect_true(all(result$Thyro >= 0))
  expect_true(all(result$T3 >= 0))
  expect_true(all(result$TSH >= 0))
  
  # Check that simulation reaches expected time
  expect_true(max(result$time) >= 168)
})

test_that("Hypothyroidism classification works correctly", {
  # Create test data
  test_data <- data.frame(
    time = c(0, 30, 60, 90, 120),
    TSH = c(1.5, 2.0, 5.5, 8.0, 12.0),
    T3 = c(4.0, 3.8, 3.2, 2.8, 2.0)
  )
  
  classification <- classify_hypothyroidism(test_data)
  
  expect_true(is.data.frame(classification))
  expect_true('hypothyroid_grade' %in% names(classification))
  expect_true(all(classification$hypothyroid_grade %in% 0:4))
  
  # Check that higher TSH leads to higher grade
  final_grade <- tail(classification$hypothyroid_grade, 1)
  expect_true(final_grade >= 2)  # Should be at least grade 2 with TSH = 12
})

test_that("Risk score calculation works correctly", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Simulate patient
  result <- simulate_patient(model, t_span = c(0, 168), drug_type = 'nivolumab')
  
  # Calculate risk score
  risk_metrics <- calculate_risk_score(model, result, time_horizon = 168)
  
  expect_true(is.list(risk_metrics))
  expect_true('grade2_hypothyroidism' %in% names(risk_metrics))
  expect_true('grade3_hypothyroidism' %in% names(risk_metrics))
  expect_true('time_to_onset_days' %in% names(risk_metrics))
  
  # Check value ranges
  expect_true(risk_metrics$grade2_hypothyroidism >= 0)
  expect_true(risk_metrics$grade2_hypothyroidism <= 1)
  expect_true(risk_metrics$grade3_hypothyroidism >= 0)
  expect_true(risk_metrics$grade3_hypothyroidism <= 1)
  
  if (!is.na(risk_metrics$time_to_onset_days)) {
    expect_true(risk_metrics$time_to_onset_days > 0)
    expect_true(risk_metrics$time_to_onset_days <= 168)
  }
})

test_that("Model diagnostics work correctly", {
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Simulate patient
  result <- simulate_patient(model, t_span = c(0, 168), drug_type = 'nivolumab')
  
  # Run diagnostics
  diagnostics <- model_diagnostics(model, result)
  
  expect_true(is.list(diagnostics))
  expect_true('mass_balance' %in% names(diagnostics))
  expect_true('stability' %in% names(diagnostics))
  expect_true('plausibility' %in% names(diagnostics))
  expect_true('summary' %in% names(diagnostics))
  
  # Check summary
  expect_true(is.logical(diagnostics$summary$mass_balance_ok))
  expect_true(is.logical(diagnostics$summary$stability_ok))
  expect_true(is.logical(diagnostics$summary$plausibility_ok))
  expect_true(is.logical(diagnostics$summary$overall_ok))
})

test_that("Numerical results match Python reference (tolerance check)", {
  # This test would compare against saved Python reference results
  # For now, we test that results are in expected ranges
  
  params <- ModelParameters$new()
  model <- QSPModel$new(params)
  
  # Standard simulation
  result <- simulate_patient(model, t_span = c(0, 168), drug_type = 'nivolumab')
  
  # Check final values are in expected ranges
  final_TSH <- tail(result$TSH, 1)
  final_T3 <- tail(result$T3, 1)
  final_IFN <- tail(result$IFN, 1)
  final_Thyro <- tail(result$Thyro, 1)
  
  # These ranges are based on expected physiological values
  expect_true(final_TSH >= 0.1 && final_TSH <= 50)  # mIU/L
  expect_true(final_T3 >= 1.0 && final_T3 <= 10)    # pmol/L
  expect_true(final_IFN >= 0 && final_IFN <= 500)   # pg/mL
  expect_true(final_Thyro >= 0.1 && final_Thyro <= 1.5)  # Normalized
  
  # Check maximum TSH reached
  max_TSH <- max(result$TSH)
  expect_true(max_TSH >= final_TSH)  # Max should be >= final
})
