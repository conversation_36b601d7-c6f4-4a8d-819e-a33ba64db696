#!/usr/bin/env python3
"""
Final test of the corrected QSP model.
"""

import numpy as np
import pandas as pd
from qsp_model_core import QSPModel, ModelParameters, simulate_patient
from qsp_population_analysis import VirtualCohort

def test_final_model():
    """Test the final corrected model."""
    
    print("=== FINAL MODEL TEST ===")
    
    # Test single patient with each drug
    params = ModelParameters()
    model = QSPModel(params)
    
    print(f"Model parameters:")
    print(f"  alpha: {params.alpha}")
    print(f"  beta: {params.beta}")
    print(f"  gamma: {params.gamma}")
    print(f"  k_death: {params.k_death}")
    print(f"  k_regen: {params.k_regen}")
    
    print(f"\nDrug binding affinities:")
    print(f"  Nivolumab Kd: {params.Kd_nivo_PD1} nM")
    print(f"  Pembrolizumab Kd: {params.Kd_pembro_PD1} nM")
    print(f"  Atezolizumab Kd: {params.Kd_atezo_PDL1} nM")
    
    # Test drug-specific effects
    print(f"\nDrug-specific immune activation (at 50M ng/mL):")
    test_conc = 50000000
    for drug in ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']:
        activation = model.checkpoint_binding(test_conc, drug)
        print(f"  {drug:12}: {activation:.6f}")
    
    # Test population simulation
    print(f"\n=== POPULATION TEST (n=20) ===")
    cohort = VirtualCohort(n_patients=20)
    results = cohort.simulate_all(t_span=(0, 168))

    print(f"Results columns: {list(results.columns)}")
    print(f"Results shape: {results.shape}")

    # Calculate incidence rates
    any_hypo = results['any_hypothyroidism'].mean() * 100
    grade2_hypo = results['grade2_hypothyroidism'].mean() * 100

    print(f"Any hypothyroidism: {any_hypo:.1f}%")
    print(f"Grade 2+ hypothyroidism: {grade2_hypo:.1f}%")

    # Drug-specific rates
    print(f"\nDrug-specific Grade 2+ rates:")
    for drug in ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']:
        drug_mask = results['drug_type'] == drug
        if drug_mask.sum() > 0:
            drug_rate = results.loc[drug_mask, 'grade2_hypothyroidism'].mean() * 100
            print(f"  {drug:12}: {drug_rate:.1f}%")

    # TSH statistics
    print(f"\nTSH statistics:")
    print(f"  Median peak TSH: {results['peak_TSH_mIU_per_L'].median():.2f} mIU/L")
    print(f"  TSH range: {results['peak_TSH_mIU_per_L'].min():.2f} - {results['peak_TSH_mIU_per_L'].max():.2f} mIU/L")
    
    return results

if __name__ == "__main__":
    test_final_model()
