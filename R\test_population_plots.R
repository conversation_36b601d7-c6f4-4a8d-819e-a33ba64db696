#!/usr/bin/env Rscript
#' Test script for population plotting functions
#' ===========================================

# Load required libraries
suppressMessages({
  library(dplyr)
  library(ggplot2)
  library(readr)
  library(purrr)
  library(futile.logger)
})

# Source QSP model components
source("R/qsp_model_core.R")
source("R/qsp_population_analysis.R")
source("R/plotting_functions.R")

# Set random seed for reproducibility
set.seed(42)

# Create sample population results for testing
create_sample_population_data <- function(n_patients = 100) {
  
  # Sample drug types
  drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  
  # Create sample data
  sample_data <- data.frame(
    patient_id = sprintf('VP%04d', 1:n_patients),
    age = rnorm(n_patients, 65, 10),
    sex = sample(c('M', 'F'), n_patients, replace = TRUE),
    HLA_DRB1_03 = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    drug_type = sample(drugs, n_patients, replace = TRUE),
    any_hypothyroidism = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)),
    grade2_hypothyroidism = sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.90, 0.10)),
    time_to_onset_days = ifelse(sample(c(0, 1), n_patients, replace = TRUE, prob = c(0.85, 0.15)) == 1,
                               rnorm(n_patients, 84, 21), NA),  # 12 weeks ± 3 weeks
    peak_TSH_mIU_per_L = rlnorm(n_patients, 1.5, 0.5),
    max_thyrocyte_loss_percent = runif(n_patients, 0, 30),
    peak_drug_concentration = rnorm(n_patients, 100, 20),
    stringsAsFactors = FALSE
  )
  
  # Ensure realistic values
  sample_data$age <- pmax(pmin(sample_data$age, 90), 18)
  sample_data$peak_TSH_mIU_per_L <- pmax(sample_data$peak_TSH_mIU_per_L, 0.1)
  sample_data$max_thyrocyte_loss_percent <- pmax(pmin(sample_data$max_thyrocyte_loss_percent, 100), 0)
  sample_data$peak_drug_concentration <- pmax(sample_data$peak_drug_concentration, 0)
  
  return(sample_data)
}

main <- function() {
  
  cat("Testing Population Plotting Functions\n")
  cat(rep("=", 50), "\n", sep = "")
  
  # Create output directory
  output_dir <- "test_population_plots"
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # Create sample data
  cat("Creating sample population data...\n")
  sample_data <- create_sample_population_data(100)
  
  # Test each plotting function
  cat("\nTesting plotting functions...\n")
  
  tryCatch({
    # Test 1: Incidence by drug
    cat("- Testing plot_incidence_by_drug...\n")
    p1 <- plot_incidence_by_drug(sample_data, 
                                 save_path = file.path(output_dir, "test_incidence_by_drug.png"))
    cat("  ✓ Incidence by drug plot created\n")
    
    # Test 2: Onset distribution
    cat("- Testing plot_onset_distribution...\n")
    p2 <- plot_onset_distribution(sample_data, 
                                  save_path = file.path(output_dir, "test_onset_distribution.png"))
    cat("  ✓ Onset distribution plot created\n")
    
    # Test 3: Risk stratification
    cat("- Testing plot_risk_stratification...\n")
    p3 <- plot_risk_stratification(sample_data, 
                                   save_path = file.path(output_dir, "test_risk_stratification.png"))
    cat("  ✓ Risk stratification plot created\n")
    
    # Test 4: Dose response
    cat("- Testing plot_dose_response...\n")
    p4 <- plot_dose_response(sample_data, 
                             save_path = file.path(output_dir, "test_dose_response.png"))
    cat("  ✓ Dose response plot created\n")
    
    # Test 5: Literature comparison
    cat("- Testing plot_literature_comparison...\n")
    p5 <- plot_literature_comparison(sample_data, 
                                     save_path = file.path(output_dir, "test_literature_comparison.png"))
    cat("  ✓ Literature comparison plot created\n")
    
    # Test 6: CTCAE validation
    cat("- Testing plot_ctcae_validation...\n")
    p6 <- plot_ctcae_validation(sample_data, 
                                save_path = file.path(output_dir, "test_ctcae_validation.png"))
    cat("  ✓ CTCAE validation plot created\n")
    
    cat("\nAll plotting functions tested successfully!\n")
    cat("Plots saved to:", output_dir, "\n")
    
  }, error = function(e) {
    cat("Error during testing:", e$message, "\n")
    cat("Error details:", toString(e), "\n")
  })
  
  # Save sample data for inspection
  write_csv(sample_data, file.path(output_dir, "sample_population_data.csv"))
  cat("Sample data saved for inspection\n")
  
  cat("\nTest completed!\n")
}

# Run main function
main() 