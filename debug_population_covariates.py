import random
import numpy as np
from qsp_population_analysis import VirtualCohort
from qsp_model_core import QSPModel, simulate_patient, ModelParameters

# Debug the population simulation to see what's different
print('Debugging population simulation covariates:')
print('='*60)

# Set fixed seed to reproduce the exact same patients
random.seed(42)
np.random.seed(42)

# Create the same cohort as before
cohort = VirtualCohort(n_patients=10)

# Look at Patient 4 specifically (the one that developed hypothyroidism)
patient_4 = cohort.patients.iloc[3]  # 0-indexed, so patient 4 is index 3

print('Patient 4 characteristics:')
for col in patient_4.index:
    print(f'  {col}: {patient_4[col]}')

print()
print('Simulating Patient 4 manually:')

# Recreate the exact same model parameters as the population simulation
params = ModelParameters()
params.sex_factor = patient_4['sex_factor']
params.age_factor = patient_4['age_factor'] 
params.HLA_factor = patient_4['HLA_factor']
params.TPO_Ab_titer = patient_4['TPO_Ab_log_titer']
params.Thyro_max = patient_4['Thyro_max']

print('Model parameters:')
print(f'  sex_factor: {params.sex_factor}')
print(f'  age_factor: {params.age_factor}')
print(f'  HLA_factor: {params.HLA_factor}')
print(f'  TPO_Ab_titer: {params.TPO_Ab_titer}')
print(f'  Thyro_max: {params.Thyro_max}')

# Create model and simulate (this will trigger susceptibility assignment)
model = QSPModel(params)

print(f'\nBefore simulation:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_assigned: {model.params.susceptibility_assigned}')

# The population simulation calls simulate_patient which assigns susceptibility
# We need to set the random seed to get the same assignment as in the population test
# Patient 4 was assigned as non-susceptible in the population test

# Let's see what susceptibility gets assigned
sim_result = simulate_patient(model, t_span=(0, 168), drug_type=patient_4['drug_type'])

print(f'\nAfter simulation:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_level: {model.params.susceptibility_level}')
print(f'  k_death (after covariates): {model.params.k_death}')

# Check results
final_TSH = sim_result.iloc[-1]['TSH']
final_IFN = sim_result.iloc[-1]['IFN']
final_thyro = sim_result.iloc[-1]['Thyro']

print(f'\nSimulation results:')
print(f'  Final TSH: {final_TSH:.2f} mIU/L')
print(f'  Final IFN: {final_IFN:.1f} pg/mL')
print(f'  Final Thyro: {final_thyro:.3f}')
print(f'  Hypothyroidism: {final_TSH > 4.5}')

# Check if this matches the population simulation result
print(f'\nExpected from population simulation:')
print(f'  TSH: 7.81 mIU/L (hypothyroidism)')
print(f'  Susceptible: False')

if final_TSH > 4.5:
    print(f'  MATCH: This patient develops hypothyroidism')
    
    # Investigate why
    if model.params.immune_susceptible:
        print(f'  Cause: Patient is susceptible')
    else:
        print(f'  ERROR: Non-susceptible patient developing hypothyroidism!')
        print(f'  Investigating covariates...')
        
        # Check if extreme covariates are causing issues
        print(f'  Covariate effects:')
        print(f'    sex_factor: {model.params.sex_factor} (1.0 = male, 1.2 = female)')
        print(f'    age_factor: {model.params.age_factor} (1.0 = young, >1.0 = older)')
        print(f'    HLA_factor: {model.params.HLA_factor} (1.0 = normal, >1.0 = risk)')
        print(f'    TPO_Ab_titer: {model.params.TPO_Ab_titer} (0 = negative, >0 = positive)')
        print(f'    Thyro_max: {model.params.Thyro_max} (thyroid mass capacity)')
        
        # Check if any of these could cause hypothyroidism directly
        if model.params.Thyro_max < 0.8:
            print(f'    WARNING: Low Thyro_max could cause hypothyroidism')
        if model.params.TPO_Ab_titer > 2.0:
            print(f'    WARNING: High TPO-Ab could cause hypothyroidism')
else:
    print(f'  NO MATCH: This patient does not develop hypothyroidism')
    print(f'  The population simulation may have different random seed effects')
