from qsp_model_core import QSPModel, simulate_patient, calculate_risk_score, ModelParameters

# Test the population simulation process step by step
print('Testing population simulation process:')
print('='*50)

# Simulate the VirtualCohort process for one patient
params = ModelParameters()
params.sex_factor = 1.0
params.age_factor = 1.0
params.HLA_factor = 1.0
params.TPO_Ab_titer = 0.0
params.Thyro_max = 1.0

# Create model and simulate (this should trigger susceptibility assignment)
model = QSPModel(params)
drug_type = 'nivolumab'

print(f'Before simulation:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_assigned: {model.params.susceptibility_assigned}')

sim_result = simulate_patient(model, t_span=(0, 168), drug_type=drug_type)

print(f'After simulation:')
print(f'  immune_susceptible: {model.params.immune_susceptible}')
print(f'  susceptibility_assigned: {model.params.susceptibility_assigned}')
print(f'  susceptibility_level: {model.params.susceptibility_level}')

# Calculate risk metrics
risk_metrics = calculate_risk_score(model, sim_result, time_horizon=168)

print(f'Risk metrics:')
print(f'  immune_susceptible: {risk_metrics["immune_susceptible"]}')
print(f'  any_hypothyroidism: {risk_metrics["any_hypothyroidism"]}')
peak_tsh = risk_metrics['peak_TSH_mIU_per_L']
print(f'  peak_TSH: {peak_tsh:.2f}')

# Test multiple patients to see susceptibility distribution
print('\nTesting 20 patients for susceptibility distribution:')
susceptible_count = 0
for i in range(20):
    params = ModelParameters()
    model = QSPModel(params)
    sim_result = simulate_patient(model, t_span=(0, 168), drug_type='nivolumab')
    if model.params.immune_susceptible:
        susceptible_count += 1
        print(f'Patient {i+1}: SUSCEPTIBLE ({model.params.susceptibility_level})')
    else:
        print(f'Patient {i+1}: non-susceptible')

print(f'\nSusceptible patients: {susceptible_count}/20 ({susceptible_count/20*100:.1f}%)')
