#!/usr/bin/env python3
"""
Debug script to track all variables during simulation to identify why cytokines aren't produced
"""

import numpy as np
import pandas as pd
from qsp_model_core import QSPModel, simulate_patient

def debug_cytokine_production():
    """Debug cytokine production step by step"""
    
    print("🔍 DEBUGGING CYTOKINE PRODUCTION")
    print("=" * 50)
    
    # Create model with high susceptibility
    model = QSPModel(patient_id="DEBUG001", damage_susceptibility=2.0)
    
    # Check initial conditions
    initial_conditions = model.get_initial_conditions()
    print(f"\n📊 INITIAL CONDITIONS:")
    for i, name in enumerate(model.state_names):
        print(f"  {name}: {initial_conditions[i]:.6f}")
    
    # Check key parameters
    p = model.params
    print(f"\n⚙️  KEY PARAMETERS:")
    print(f"  immune_susceptible: {p.immune_susceptible}")
    print(f"  epsilon (cytokine production): {p.epsilon}")
    print(f"  k_clear_IFN (cytokine clearance): {p.k_clear_IFN}")
    print(f"  alpha (T-cell activation): {p.alpha}")
    print(f"  beta (T-cell decay): {p.beta}")
    print(f"  gamma (checkpoint effect): {p.gamma}")
    print(f"  delta (IL-2 effect): {p.delta}")
    
    # Test drug concentrations and thresholds
    drug_type = 'nivolumab'
    t_test = 7.0  # day 7
    
    drug_conc = model.drug_concentration(t_test, drug_type)
    drug_thresholds = {
        'nivolumab': 12000.0,
        'pembrolizumab': 10000.0, 
        'atezolizumab': 70000.0,
        'durvalumab': 5000.0
    }
    activation_threshold = drug_thresholds[drug_type]
    
    print(f"\n💊 DRUG ANALYSIS (Day {t_test}, {drug_type}):")
    print(f"  Drug concentration: {drug_conc:,.0f} ng/mL")
    print(f"  Activation threshold: {activation_threshold:,.0f} ng/mL")
    print(f"  Above threshold: {drug_conc >= activation_threshold}")
    
    # Test checkpoint binding
    R_new = model.checkpoint_binding(drug_conc, drug_type)
    print(f"  Checkpoint binding (R_new): {R_new:.6f}")
    
    # Simulate and track variables
    print(f"\n🚀 RUNNING SIMULATION...")
    
    results = simulate_patient(
        model=model,
        t_span=(0, 30),  # 30 days
        drug_type=drug_type
    )
    
    # Analyze results
    time = results['time']
    R = results['R']
    T_eff = results['T_eff'] 
    IFN = results['IFN']
    
    print(f"\n📈 SIMULATION RESULTS (first 10 days):")
    print("Day   R        T_eff     IFN      Drug_conc")
    print("-" * 45)
    
    for i in range(0, min(11, len(time))):
        if time[i] <= 10:
            drug_c = model.drug_concentration(time[i], drug_type)
            print(f"{time[i]:3.0f}  {R[i]:8.6f}  {T_eff[i]:8.1f}  {IFN[i]:8.1f}  {drug_c:8.0f}")
    
    # Check maximum values
    print(f"\n📊 MAXIMUM VALUES:")
    print(f"  Max T_eff: {np.max(T_eff):,.1f} cells/L")
    print(f"  Max IFN: {np.max(IFN):,.1f} pg/mL")
    print(f"  Max drug conc: {np.max([model.drug_concentration(t, drug_type) for t in time]):,.0f} ng/mL")
    
    # Manual calculation of cytokine production at day 7
    print(f"\n🧮 MANUAL CALCULATION (Day {t_test}):")
    
    # Find closest time point
    idx = np.argmin(np.abs(time - t_test))
    t_actual = time[idx]
    T_eff_val = T_eff[idx]
    IFN_val = IFN[idx]
    
    print(f"  Time: {t_actual:.1f}")
    print(f"  T_eff: {T_eff_val:.1f}")
    print(f"  IFN: {IFN_val:.1f}")
    
    # Calculate expected cytokine production
    base_production = p.epsilon * T_eff_val
    amplification_factor = 1.0 + (IFN_val / (IFN_val + 50.0))
    amplified_production = base_production * amplification_factor
    clearance = p.k_clear_IFN * IFN_val
    net_production = amplified_production - clearance
    
    print(f"  Base production (epsilon * T_eff): {base_production:.3f}")
    print(f"  Amplification factor: {amplification_factor:.3f}")
    print(f"  Amplified production: {amplified_production:.3f}")
    print(f"  Clearance: {clearance:.3f}")
    print(f"  Net dIFN/dt: {net_production:.3f}")

if __name__ == "__main__":
    debug_cytokine_production()