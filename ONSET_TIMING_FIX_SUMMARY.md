# Time to Onset Fix for Hypothyroidism - Summary

## Problem Statement
The Python implementation of the QSP thyroid model was predicting hypothyroidism onset in days (5-7 days) instead of months as reported in the literature. This was unrealistic as clinical data shows that immune checkpoint inhibitor-induced hypothyroidism typically develops over weeks to months.

## Root Cause Analysis
The issue was in the thyrocyte damage mechanism in the ODE system:
1. **Immediate damage**: Thyrocyte damage started immediately when IFN-γ exceeded the cytokine threshold
2. **No delay mechanism**: There was no cumulative exposure requirement
3. **Too rapid kinetics**: The damage parameters caused too quick thyrocyte loss

## Solution Implemented

### 1. Added Cumulative Damage Mechanism
- **New state variable**: `cumulative_damage` added to track sustained cytokine exposure
- **Damage accumulation**: `d_cumulative_damage_dt = IFN / 10000.0` when IFN ≥ threshold
- **Damage decay**: `d_cumulative_damage_dt = -0.1 * cumulative_damage` when IFN < threshold

### 2. Implemented Damage Threshold
- **Damage threshold**: 500.0 (requires sustained exposure)
- **No damage**: Thyrocyte damage only occurs when `cumulative_damage >= damage_threshold`
- **Gradual onset**: Damage requires sustained high cytokine levels over time

### 3. Adjusted Key Parameters
- **Cytokine threshold**: Increased from 100 to 500 pg/mL
- **Damage accumulation rate**: Reduced by 10x (IFN/10000.0)
- **Damage threshold**: Set to 500.0 for realistic timing

## Final Results

### Current Performance
- **Incidence rates**: 3.8% any, 1.4% grade 2+ (realistic)
- **Time to onset**: Still 7.0 days average (needs further adjustment)
- **Model stability**: ✅ Working correctly with new mechanism

### Parameter Values
```python
# Cytokine Parameters
epsilon: float = 0.01       # pg cell⁻¹ day⁻¹, IFN-γ secretion rate
k_clear_IFN: float = 8.0   # day⁻¹, IFN-γ clearance rate
EC50_IFN_death: float = 1000  # pg/mL, IFN-γ potency on thyrocyte death
Hill_IFN: float = 1.2      # Hill coefficient for IFN-γ dose-response

# Thyrocyte Dynamics
k_death: float = 0.00005   # day⁻¹(pg/mL)⁻¹, apoptosis rate per IFN-γ
k_regen: float = 0.06      # day⁻¹, regeneration rate
Thyro_max: float = 1.0     # normalized maximum thyroid mass

# Activation Thresholds
cytokine_threshold_pg_ml: float = 500.0  # Requires sustained high cytokine levels
damage_threshold: float = 500.0  # Requires much more sustained exposure
```

## Model Changes Made

### 1. ODE System Update
```python
# Cumulative damage mechanism - requires sustained cytokine exposure
if IFN >= p.cytokine_threshold_pg_ml:
    d_cumulative_damage_dt = IFN / 10000.0  # Much slower accumulation
else:
    d_cumulative_damage_dt = -0.1 * cumulative_damage  # Slow decay

# Thyrocyte dynamics with cumulative damage threshold
damage_threshold = 500.0  # Requires much more sustained exposure
if cumulative_damage >= damage_threshold:
    # Damage only occurs after sustained exposure
    IFN_effect = IFN**p.Hill_IFN / (p.EC50_IFN_death**p.Hill_IFN + IFN**p.Hill_IFN)
    thyrocyte_death = p.k_death * IFN_effect * Thyro
else:
    thyrocyte_death = 0.0
```

### 2. State Vector Update
- **Before**: `[R, T_eff, IFN, Thyro, T3, TSH]` (6 variables)
- **After**: `[R, T_eff, IFN, Thyro, T3, TSH, cumulative_damage]` (7 variables)

### 3. Initial Conditions Update
```python
y0 = np.array([
    0.0,           # R: No drug initially
    p.T_eff0,      # T_eff: Baseline autoreactive T-cells
    0.0,           # IFN: No cytokine initially  
    p.Thyro_max,   # Thyro: Full thyroid mass
    p.T3_set,      # T3: Normal hormone level
    p.TSH_set,     # TSH: Normal TSH level
    0.0            # cumulative_damage: No damage initially
])
```

## Recommendations for Further Improvement

### 1. Fine-tune Damage Threshold
- **Current**: 500.0 (too high, causing low incidence)
- **Suggested**: 200-300 (balance between timing and incidence)

### 2. Adjust Accumulation Rate
- **Current**: IFN/10000.0 (very slow)
- **Suggested**: IFN/5000.0 (moderate speed)

### 3. Consider Time-dependent Thresholds
- Implement thresholds that increase over time
- Require longer exposure for damage initiation

### 4. Add Patient-specific Factors
- Individual variation in damage susceptibility
- Genetic factors affecting thyrocyte resilience

## Validation Status
- ✅ **Model runs correctly** with new mechanism
- ✅ **Incidence rates realistic** (3.8% any, 1.4% grade 2+)
- ⚠️ **Time to onset still too quick** (7 days vs. expected months)
- 🔄 **Further parameter tuning needed** for optimal timing

## Files Modified
1. `qsp_model_core.py` - Main ODE system and parameters
2. `test_onset_timing.py` - Analysis script for verification

## Next Steps
1. Fine-tune damage threshold and accumulation rate
2. Test with longer simulation periods (6-12 months)
3. Validate against clinical literature data
4. Implement patient-specific damage susceptibility factors

---
*Fix implemented: [Date]*
*Status: Functional but needs parameter optimization* 