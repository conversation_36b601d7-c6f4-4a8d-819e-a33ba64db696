import random
from qsp_model_core import QSPModel, ModelParameters

# Test susceptibility assignment directly
print('Testing susceptibility assignment:')
print('='*50)

# Set fixed seed
random.seed(42)

# Test multiple patients with nivolumab
susceptible_count = 0
for i in range(20):
    params = ModelParameters()
    model = QSPModel(params)
    
    print(f'Patient {i+1:2d}: Before assignment - susceptible: {model.params.immune_susceptible}, assigned: {model.params.susceptibility_assigned}')
    
    # Call assignment directly
    model._assign_immune_susceptibility('nivolumab')
    
    print(f'           After assignment  - susceptible: {model.params.immune_susceptible}, level: {model.params.susceptibility_level}')
    
    if model.params.immune_susceptible:
        susceptible_count += 1

print(f'\nSusceptible patients: {susceptible_count}/20 ({susceptible_count/20*100:.1f}%)')
print(f'Expected for nivolumab: ~8% (1-2 patients)')

# Test the thresholds directly
print('\n' + '='*50)
print('Testing threshold calculation:')

drug_susceptibility_rates = {
    'nivolumab': 0.08,      # 8% target incidence
    'pembrolizumab': 0.12,  # 12% target incidence
    'atezolizumab': 0.055,  # 5.5% target incidence
    'durvalumab': 0.05      # 5% target incidence
}

for drug, rate in drug_susceptibility_rates.items():
    non_susceptible_threshold = 1.0 - rate
    low_susceptible_threshold = non_susceptible_threshold + rate * 0.7
    
    print(f'{drug}:')
    print(f'  Total susceptible rate: {rate*100:.1f}%')
    print(f'  Non-susceptible threshold: {non_susceptible_threshold:.3f} (0 to {non_susceptible_threshold:.3f})')
    print(f'  Low-susceptible threshold: {low_susceptible_threshold:.3f} ({non_susceptible_threshold:.3f} to {low_susceptible_threshold:.3f})')
    print(f'  High-susceptible range: {low_susceptible_threshold:.3f} to 1.000')

# Test with specific random values
print('\n' + '='*50)
print('Testing with specific random values:')

test_values = [0.5, 0.9, 0.92, 0.95, 0.98, 0.99]
for val in test_values:
    # Nivolumab thresholds
    non_susc_thresh = 1.0 - 0.08  # 0.92
    low_susc_thresh = 0.92 + 0.08 * 0.7  # 0.976
    
    if val < non_susc_thresh:
        result = "non-susceptible"
    elif val < low_susc_thresh:
        result = "low-susceptible"
    else:
        result = "high-susceptible"
    
    print(f'Random value {val:.3f}: {result}')
