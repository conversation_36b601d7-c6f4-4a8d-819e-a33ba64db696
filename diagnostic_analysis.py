#!/usr/bin/env python3
"""
Diagnostic Analysis Script for QSP Thyroid Model
This script analyzes cytokine levels, damage accumulation, and identifies
why the current model is not achieving realistic hypothyroidism rates.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from qsp_model_core import QSPModel, simulate_patient
from typing import Dict, List, <PERSON><PERSON>

def diagnose_cytokine_levels(patient_id: str = "DIAG001", 
                           damage_susceptibility: float = 2.0,
                           drug_type: str = 'nivolumab',
                           t_span: Tuple[float, float] = (0, 365)) -> Dict:
    """
    Comprehensive diagnostic analysis of cytokine levels and damage accumulation.
    
    Args:
        patient_id: Patient identifier
        damage_susceptibility: Patient damage susceptibility (use high value for testing)
        drug_type: Type of checkpoint inhibitor
        t_span: Time span for simulation (days)
        
    Returns:
        Dictionary with diagnostic results
    """
    print(f"🔍 DIAGNOSTIC ANALYSIS: {drug_type.upper()}")
    print("=" * 60)
    
    # Create high-susceptibility patient for maximum sensitivity
    model = QSPModel(patient_id=patient_id, damage_susceptibility=damage_susceptibility)
    
    # Run simulation with detailed time points
    results = simulate_patient(model, t_span=t_span, drug_type=drug_type)
    
    # Extract key variables
    time_days = results['time']
    IFN_levels = results['IFN']
    cumulative_damage = results['cumulative_damage']
    Thyro_levels = results['Thyro']
    TSH_levels = results['TSH']
    T3_levels = results['T3']
    
    # Calculate diagnostic metrics
    diagnostics = {}
    
    # 1. Cytokine Analysis
    max_IFN = np.max(IFN_levels)
    mean_IFN = np.mean(IFN_levels)
    time_above_threshold = np.sum(IFN_levels >= model.params.cytokine_threshold_pg_ml) * (time_days[1] - time_days[0])
    peak_IFN_day = time_days[np.argmax(IFN_levels)]
    
    diagnostics['cytokine_analysis'] = {
        'max_IFN_pg_ml': max_IFN,
        'mean_IFN_pg_ml': mean_IFN,
        'cytokine_threshold_pg_ml': model.params.cytokine_threshold_pg_ml,
        'time_above_threshold_days': time_above_threshold,
        'peak_IFN_day': peak_IFN_day,
        'threshold_ratio': max_IFN / model.params.cytokine_threshold_pg_ml
    }
    
    # 2. Damage Analysis
    max_damage = np.max(cumulative_damage)
    final_damage = cumulative_damage.iloc[-1] if hasattr(cumulative_damage, 'iloc') else cumulative_damage[-1]
    damage_threshold = model.params.base_damage_threshold
    time_dependent_threshold_final = damage_threshold + (t_span[1] / 30.0) * model.params.damage_threshold_growth_rate
    
    diagnostics['damage_analysis'] = {
        'max_cumulative_damage': max_damage,
        'final_cumulative_damage': final_damage,
        'base_damage_threshold': damage_threshold,
        'final_time_dependent_threshold': time_dependent_threshold_final,
        'damage_threshold_ratio': max_damage / damage_threshold,
        'damage_susceptibility': damage_susceptibility,
        'damage_accumulation_rate': model.params.damage_accumulation_rate
    }
    
    # 3. Thyrocyte Analysis
    initial_thyro = Thyro_levels.iloc[0] if hasattr(Thyro_levels, 'iloc') else Thyro_levels[0]
    final_thyro = Thyro_levels.iloc[-1] if hasattr(Thyro_levels, 'iloc') else Thyro_levels[-1]
    min_thyro = np.min(Thyro_levels)
    thyro_loss_percent = ((initial_thyro - min_thyro) / initial_thyro) * 100
    
    diagnostics['thyrocyte_analysis'] = {
        'initial_thyrocytes': initial_thyro,
        'final_thyrocytes': final_thyro,
        'min_thyrocytes': min_thyro,
        'thyrocyte_loss_percent': thyro_loss_percent
    }
    
    # 4. Clinical Outcomes
    max_TSH = np.max(TSH_levels)
    min_T3 = np.min(T3_levels)
    hypothyroidism_detected = max_TSH > 4.5 or min_T3 < 3.1
    
    diagnostics['clinical_outcomes'] = {
        'max_TSH_mIU_per_L': max_TSH,
        'min_T3_pmol_per_L': min_T3,
        'hypothyroidism_detected': hypothyroidism_detected,
        'TSH_normal_range': (0.4, 4.5),
        'T3_normal_range': (3.1, 6.8)
    }
    
    # 5. Print Diagnostic Summary
    print_diagnostic_summary(diagnostics)
    
    # 6. Create diagnostic plots
    create_diagnostic_plots(time_days, IFN_levels, cumulative_damage, Thyro_levels, 
                          TSH_levels, T3_levels, diagnostics, drug_type)
    
    return diagnostics

def print_diagnostic_summary(diagnostics: Dict) -> None:
    """Print comprehensive diagnostic summary."""
    
    print("\n📊 CYTOKINE ANALYSIS")
    print("-" * 40)
    cytokine = diagnostics['cytokine_analysis']
    print(f"  Max IFN-γ level: {cytokine['max_IFN_pg_ml']:.2f} pg/mL")
    print(f"  Mean IFN-γ level: {cytokine['mean_IFN_pg_ml']:.2f} pg/mL")
    print(f"  Cytokine threshold: {cytokine['cytokine_threshold_pg_ml']:.2f} pg/mL")
    print(f"  Time above threshold: {cytokine['time_above_threshold_days']:.1f} days")
    print(f"  Peak IFN-γ day: {cytokine['peak_IFN_day']:.1f}")
    print(f"  Threshold ratio: {cytokine['threshold_ratio']:.2f} (>1.0 needed for activation)")
    
    if cytokine['threshold_ratio'] < 1.0:
        print("  ⚠️  ISSUE: IFN-γ levels never exceed threshold!")
    
    print("\n🎯 DAMAGE ANALYSIS")
    print("-" * 40)
    damage = diagnostics['damage_analysis']
    print(f"  Max cumulative damage: {damage['max_cumulative_damage']:.3f}")
    print(f"  Final cumulative damage: {damage['final_cumulative_damage']:.3f}")
    print(f"  Base damage threshold: {damage['base_damage_threshold']:.3f}")
    print(f"  Final time-dependent threshold: {damage['final_time_dependent_threshold']:.3f}")
    print(f"  Damage threshold ratio: {damage['damage_threshold_ratio']:.2f} (>1.0 needed for damage)")
    print(f"  Damage susceptibility: {damage['damage_susceptibility']:.1f}")
    print(f"  Accumulation rate: IFN/{damage['damage_accumulation_rate']:.0f}")
    
    if damage['damage_threshold_ratio'] < 1.0:
        print("  ⚠️  ISSUE: Cumulative damage never exceeds threshold!")
    
    print("\n🦠 THYROCYTE ANALYSIS")
    print("-" * 40)
    thyro = diagnostics['thyrocyte_analysis']
    print(f"  Initial thyrocytes: {thyro['initial_thyrocytes']:.3f}")
    print(f"  Final thyrocytes: {thyro['final_thyrocytes']:.3f}")
    print(f"  Min thyrocytes: {thyro['min_thyrocytes']:.3f}")
    print(f"  Thyrocyte loss: {thyro['thyrocyte_loss_percent']:.1f}%")
    
    print("\n🏥 CLINICAL OUTCOMES")
    print("-" * 40)
    clinical = diagnostics['clinical_outcomes']
    print(f"  Max TSH: {clinical['max_TSH_mIU_per_L']:.2f} mIU/L (normal: {clinical['TSH_normal_range'][0]}-{clinical['TSH_normal_range'][1]})")
    print(f"  Min T3: {clinical['min_T3_pmol_per_L']:.2f} pmol/L (normal: {clinical['T3_normal_range'][0]}-{clinical['T3_normal_range'][1]})")
    print(f"  Hypothyroidism detected: {'✅ YES' if clinical['hypothyroidism_detected'] else '❌ NO'}")

def create_diagnostic_plots(time_days, IFN_levels, cumulative_damage, Thyro_levels, 
                          TSH_levels, T3_levels, diagnostics, drug_type):
    """Create comprehensive diagnostic plots."""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Diagnostic Analysis: {drug_type.upper()}', fontsize=16, fontweight='bold')
    
    # Plot 1: IFN-γ levels over time
    ax1 = axes[0, 0]
    ax1.plot(time_days, IFN_levels, 'b-', linewidth=2, label='IFN-γ')
    ax1.axhline(y=diagnostics['cytokine_analysis']['cytokine_threshold_pg_ml'], 
                color='r', linestyle='--', label='Threshold')
    ax1.set_xlabel('Time (days)')
    ax1.set_ylabel('IFN-γ (pg/mL)')
    ax1.set_title('Cytokine Levels')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Cumulative damage vs threshold
    ax2 = axes[0, 1]
    ax2.plot(time_days, cumulative_damage, 'g-', linewidth=2, label='Cumulative Damage')
    
    # Time-dependent threshold
    base_threshold = diagnostics['damage_analysis']['base_damage_threshold']
    growth_rate = diagnostics['damage_analysis']['damage_accumulation_rate']  # This should be growth rate, will fix
    time_dependent_threshold = base_threshold + (time_days / 30.0) * 0.05  # Use actual growth rate
    ax2.plot(time_days, [base_threshold] * len(time_days), 'r--', label='Base Threshold')
    ax2.plot(time_days, time_dependent_threshold, 'orange', linestyle='--', label='Time-dependent Threshold')
    ax2.set_xlabel('Time (days)')
    ax2.set_ylabel('Cumulative Damage')
    ax2.set_title('Damage Accumulation')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Thyrocyte levels
    ax3 = axes[0, 2]
    ax3.plot(time_days, Thyro_levels, 'm-', linewidth=2)
    ax3.set_xlabel('Time (days)')
    ax3.set_ylabel('Thyrocytes (normalized)')
    ax3.set_title('Thyrocyte Population')
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: TSH levels
    ax4 = axes[1, 0]
    ax4.plot(time_days, TSH_levels, 'c-', linewidth=2)
    ax4.axhline(y=4.5, color='r', linestyle='--', label='Upper Normal (4.5)')
    ax4.set_xlabel('Time (days)')
    ax4.set_ylabel('TSH (mIU/L)')
    ax4.set_title('TSH Levels')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # Plot 5: T3 levels
    ax5 = axes[1, 1]
    ax5.plot(time_days, T3_levels, 'y-', linewidth=2)
    ax5.axhline(y=3.1, color='r', linestyle='--', label='Lower Normal (3.1)')
    ax5.set_xlabel('Time (days)')
    ax5.set_ylabel('T3 (pmol/L)')
    ax5.set_title('T3 Levels')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # Plot 6: Summary metrics
    ax6 = axes[1, 2]
    metrics = [
        diagnostics['cytokine_analysis']['threshold_ratio'],
        diagnostics['damage_analysis']['damage_threshold_ratio'],
        diagnostics['thyrocyte_analysis']['thyrocyte_loss_percent'] / 100,
        1.0 if diagnostics['clinical_outcomes']['hypothyroidism_detected'] else 0.0
    ]
    labels = ['IFN Threshold\nRatio', 'Damage Threshold\nRatio', 'Thyrocyte\nLoss %', 'Hypothyroidism\nDetected']
    colors = ['blue', 'green', 'magenta', 'red']
    
    bars = ax6.bar(labels, metrics, color=colors, alpha=0.7)
    ax6.axhline(y=1.0, color='red', linestyle='--', alpha=0.5, label='Target = 1.0')
    ax6.set_ylabel('Ratio / Percentage')
    ax6.set_title('Key Diagnostic Metrics')
    ax6.legend()
    
    # Add value labels on bars
    for bar, metric in zip(bars, metrics):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{metric:.2f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(f'diagnostic_plots_{drug_type}.png', dpi=300, bbox_inches='tight')
    plt.show()

def compare_drug_diagnostics() -> None:
    """Compare diagnostic results across different checkpoint inhibitors."""
    
    print("\n🔬 MULTI-DRUG DIAGNOSTIC COMPARISON")
    print("=" * 80)
    
    drugs = ['nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab']
    all_diagnostics = {}
    
    for drug in drugs:
        print(f"\nAnalyzing {drug}...")
        diagnostics = diagnose_cytokine_levels(
            patient_id=f"DIAG_{drug.upper()}",
            damage_susceptibility=2.0,  # Use maximum susceptibility
            drug_type=drug,
            t_span=(0, 365)
        )
        all_diagnostics[drug] = diagnostics
    
    # Create comparison summary
    print("\n📊 CROSS-DRUG COMPARISON SUMMARY")
    print("=" * 80)
    
    comparison_df = pd.DataFrame({
        drug: {
            'Max_IFN_pg_ml': diag['cytokine_analysis']['max_IFN_pg_ml'],
            'IFN_Threshold_Ratio': diag['cytokine_analysis']['threshold_ratio'],
            'Max_Damage': diag['damage_analysis']['max_cumulative_damage'],
            'Damage_Threshold_Ratio': diag['damage_analysis']['damage_threshold_ratio'],
            'Thyrocyte_Loss_%': diag['thyrocyte_analysis']['thyrocyte_loss_percent'],
            'Hypothyroidism_Detected': diag['clinical_outcomes']['hypothyroidism_detected']
        }
        for drug, diag in all_diagnostics.items()
    }).T
    
    print(comparison_df.round(3))
    
    # Save comparison results
    comparison_df.to_csv('diagnostic_comparison_results.csv')
    print(f"\n💾 Results saved to: diagnostic_comparison_results.csv")
    
    return all_diagnostics

def identify_root_causes(diagnostics: Dict) -> List[str]:
    """Identify root causes preventing hypothyroidism development."""
    
    issues = []
    
    # Check cytokine levels
    if diagnostics['cytokine_analysis']['threshold_ratio'] < 1.0:
        issues.append("CYTOKINE INSUFFICIENT: IFN-γ levels never exceed activation threshold")
    
    if diagnostics['cytokine_analysis']['time_above_threshold_days'] < 30:
        issues.append("CYTOKINE DURATION: Insufficient time above threshold for sustained damage")
    
    # Check damage accumulation
    if diagnostics['damage_analysis']['damage_threshold_ratio'] < 1.0:
        issues.append("DAMAGE INSUFFICIENT: Cumulative damage never exceeds threshold")
    
    # Check thyrocyte loss
    if diagnostics['thyrocyte_analysis']['thyrocyte_loss_percent'] < 10:
        issues.append("THYROCYTE PRESERVATION: Insufficient thyrocyte loss for clinical impact")
    
    return issues

def main():
    """Main diagnostic analysis function."""
    
    print("🚀 STARTING COMPREHENSIVE DIAGNOSTIC ANALYSIS")
    print("=" * 80)
    
    # Single drug detailed analysis
    diagnostics = diagnose_cytokine_levels(
        patient_id="DIAG_DETAILED",
        damage_susceptibility=2.0,  # Maximum susceptibility
        drug_type='nivolumab',
        t_span=(0, 365)
    )
    
    # Identify root causes
    print("\n🔍 ROOT CAUSE ANALYSIS")
    print("-" * 40)
    issues = identify_root_causes(diagnostics)
    
    if issues:
        print("❌ IDENTIFIED ISSUES:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("✅ No major issues identified")
    
    # Multi-drug comparison
    all_diagnostics = compare_drug_diagnostics()
    
    print("\n🎯 NEXT STEPS RECOMMENDATIONS")
    print("-" * 40)
    print("1. INCREASE cytokine production (epsilon parameter)")
    print("2. REDUCE cytokine clearance (k_clear_IFN parameter)")
    print("3. LOWER cytokine activation threshold")
    print("4. IMPLEMENT cytokine amplification feedback")
    print("5. CONSIDER alternative damage mechanisms")
    
    return diagnostics, all_diagnostics

if __name__ == "__main__":
    diagnostics, all_diagnostics = main()