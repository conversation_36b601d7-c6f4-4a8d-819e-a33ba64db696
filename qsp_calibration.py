#!/usr/bin/env python3
"""
QSP Model Calibration: Parameter Estimation and Validation
==========================================================

This module implements parameter estimation methods for the QSP model using:
- Gaussian Process surrogate optimization
- Bayesian parameter estimation with MCMC
- Cross-validation and uncertainty quantification
- External validation against clinical data

Author: QSP Modeling Team
Date: 2024
License: MIT

Usage:
    from qsp_calibration import GPCalibrator, BayesianCalibrator
    
    calibrator = GPCalibrator(training_data)
    best_params = calibrator.optimize()
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Callable
import pickle
import logging
from dataclasses import dataclass
from scipy.optimize import minimize
from scipy.stats import norm, lognorm
import warnings

# Optional imports for advanced methods
try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer
    from skopt.utils import use_named_args
    HAS_SKOPT = True
except ImportError:
    HAS_SKOPT = False
    warnings.warn("scikit-optimize not available. GP optimization disabled.")

try:
    import emcee
    HAS_EMCEE = True
except ImportError:
    HAS_EMCEE = False
    warnings.warn("emcee not available. MCMC sampling disabled.")

from qsp_model_core import QSPModel, ModelParameters, simulate_patient

logger = logging.getLogger(__name__)

@dataclass
class CalibrationData:
    """Container for calibration dataset with patient data and outcomes."""
    
    patient_ids: List[str]
    demographics: pd.DataFrame  # age, sex, BMI, etc.
    baseline_labs: pd.DataFrame  # TSH, T3, T4, TPO-Ab, etc.
    longitudinal_data: pd.DataFrame  # time-series biomarkers
    outcomes: pd.DataFrame  # hypothyroidism events, timing
    drug_regimens: pd.DataFrame  # drug type, dosing
    
    def __post_init__(self):
        """Validate data consistency."""
        n_patients = len(self.patient_ids)
        
        assert len(self.demographics) == n_patients
        assert len(self.baseline_labs) == n_patients
        assert len(self.outcomes) == n_patients
        assert len(self.drug_regimens) == n_patients
        
        logger.info(f"Calibration data loaded: {n_patients} patients")

class ObjectiveFunction:
    """
    Objective function for parameter optimization.
    
    Computes weighted sum of multiple endpoints:
    - IFN-γ kinetics RMSE
    - TSH dynamics RMSE  
    - Hypothyroidism incidence error
    - Time-to-onset distribution error
    """
    
    def __init__(self, 
                 calibration_data: CalibrationData,
                 weights: Optional[Dict[str, float]] = None):
        """
        Initialize objective function.
        
        Args:
            calibration_data: Training dataset
            weights: Relative weights for different endpoints
        """
        self.data = calibration_data
        self.weights = weights or {
            'ifn_rmse': 0.3,
            'tsh_rmse': 0.3, 
            'incidence_error': 0.2,
            'timing_error': 0.2
        }
        
        # Precompute observed statistics
        self._compute_observed_stats()
    
    def _compute_observed_stats(self):
        """Precompute observed statistics for efficiency."""
        
        # Observed incidence rates
        self.obs_any_hypo = self.data.outcomes['any_hypothyroidism'].mean()
        self.obs_grade2_hypo = self.data.outcomes['grade2_hypothyroidism'].mean()
        
        # Observed time-to-onset distribution
        onset_times = self.data.outcomes[self.data.outcomes['grade2_hypothyroidism'] == 1]['time_to_onset']
        self.obs_median_onset = onset_times.median()
        self.obs_onset_iqr = onset_times.quantile([0.25, 0.75]).values
        
        # Extract longitudinal biomarker data
        self.obs_ifn_data = self.data.longitudinal_data[['patient_id', 'time', 'IFN_gamma']].dropna()
        self.obs_tsh_data = self.data.longitudinal_data[['patient_id', 'time', 'TSH']].dropna()
        
        logger.info(f"Observed statistics computed: {self.obs_any_hypo:.1%} incidence, "
                   f"{self.obs_median_onset:.1f} days median onset")
    
    def __call__(self, params_vector: np.ndarray) -> float:
        """
        Evaluate objective function for given parameter vector.
        
        Args:
            params_vector: Parameter values to evaluate
            
        Returns:
            Objective function value (lower is better)
        """
        
        try:
            # Convert parameter vector to ModelParameters object
            model_params = self._vector_to_params(params_vector)
            
            # Simulate all patients
            sim_results = self._simulate_cohort(model_params)
            
            # Compute individual error components
            ifn_rmse = self._compute_ifn_rmse(sim_results)
            tsh_rmse = self._compute_tsh_rmse(sim_results)
            incidence_error = self._compute_incidence_error(sim_results)
            timing_error = self._compute_timing_error(sim_results)
            
            # Weighted objective
            objective = (self.weights['ifn_rmse'] * ifn_rmse +
                        self.weights['tsh_rmse'] * tsh_rmse +
                        self.weights['incidence_error'] * incidence_error +
                        self.weights['timing_error'] * timing_error)
            
            logger.debug(f"Objective: {objective:.4f} (IFN: {ifn_rmse:.4f}, "
                        f"TSH: {tsh_rmse:.4f}, Inc: {incidence_error:.4f}, "
                        f"Time: {timing_error:.4f})")
            
            return objective
            
        except Exception as e:
            logger.warning(f"Simulation failed: {e}")
            return 1e6  # Large penalty for failed simulations
    
    def _vector_to_params(self, params_vector: np.ndarray) -> ModelParameters:
        """Convert parameter vector to ModelParameters object."""
        
        # Define parameter mapping (log-transformed for positive parameters)
        param_names = [
            'k_death', 'EC50_IFN_death', 'k_regen', 'epsilon', 
            'k_clear_IFN', 'alpha', 'beta', 'gamma'
        ]
        
        params = ModelParameters()
        
        for i, name in enumerate(param_names):
            if name in ['k_death', 'EC50_IFN_death', 'k_regen', 'epsilon', 'k_clear_IFN']:
                # Log-normal parameters
                setattr(params, name, np.exp(params_vector[i]))
            else:
                # Normal parameters
                setattr(params, name, params_vector[i])
        
        return params
    
    def _simulate_cohort(self, model_params: ModelParameters) -> List[pd.DataFrame]:
        """Simulate entire patient cohort with given parameters."""
        
        results = []
        
        for i, patient_id in enumerate(self.data.patient_ids):
            # Set patient-specific covariates
            patient_params = model_params
            
            # Demographics
            demo = self.data.demographics.iloc[i]
            patient_params.sex_factor = 1.3 if demo['sex'] == 'F' else 1.0
            patient_params.age_factor = 1.2 if demo['age'] > 60 else 1.0
            
            # Baseline labs
            labs = self.data.baseline_labs.iloc[i]
            patient_params.TPO_Ab_titer = np.log10(max(labs['TPO_Ab'], 1))
            patient_params.HLA_factor = 2.2 if labs['HLA_DRB1_03'] else 1.0
            
            # Drug regimen
            drug_info = self.data.drug_regimens.iloc[i]
            drug_type = drug_info['drug_type']
            
            # Create model and simulate
            model = QSPModel(patient_params)
            sim_result = simulate_patient(model, t_span=(0, 168), drug_type=drug_type)
            sim_result['patient_id'] = patient_id
            
            results.append(sim_result)
        
        return results
    
    def _compute_ifn_rmse(self, sim_results: List[pd.DataFrame]) -> float:
        """Compute RMSE for IFN-γ kinetics."""
        
        errors = []
        
        for sim_data in sim_results:
            patient_id = sim_data['patient_id'].iloc[0]
            
            # Get observed data for this patient
            obs_data = self.obs_ifn_data[self.obs_ifn_data['patient_id'] == patient_id]
            
            if len(obs_data) == 0:
                continue
            
            # Interpolate simulated data to observed time points
            for _, obs_row in obs_data.iterrows():
                t_obs = obs_row['time']
                ifn_obs = obs_row['IFN_gamma']
                
                # Find closest simulated time point
                time_diffs = np.abs(sim_data['time'] - t_obs)
                closest_idx = time_diffs.idxmin()
                ifn_sim = sim_data.loc[closest_idx, 'IFN']
                
                # Log-scale error (biomarkers often log-normal)
                error = np.log(max(ifn_sim, 1)) - np.log(max(ifn_obs, 1))
                errors.append(error)
        
        return np.sqrt(np.mean(np.array(errors)**2)) if errors else 1.0
    
    def _compute_tsh_rmse(self, sim_results: List[pd.DataFrame]) -> float:
        """Compute RMSE for TSH dynamics."""
        
        errors = []
        
        for sim_data in sim_results:
            patient_id = sim_data['patient_id'].iloc[0]
            
            # Get observed data for this patient
            obs_data = self.obs_tsh_data[self.obs_tsh_data['patient_id'] == patient_id]
            
            if len(obs_data) == 0:
                continue
            
            # Interpolate simulated data to observed time points
            for _, obs_row in obs_data.iterrows():
                t_obs = obs_row['time']
                tsh_obs = obs_row['TSH']
                
                # Find closest simulated time point
                time_diffs = np.abs(sim_data['time'] - t_obs)
                closest_idx = time_diffs.idxmin()
                tsh_sim = sim_data.loc[closest_idx, 'TSH']
                
                # Relative error
                error = (tsh_sim - tsh_obs) / max(tsh_obs, 1)
                errors.append(error)
        
        return np.sqrt(np.mean(np.array(errors)**2)) if errors else 1.0
    
    def _compute_incidence_error(self, sim_results: List[pd.DataFrame]) -> float:
        """Compute error in hypothyroidism incidence rates."""
        
        # Compute simulated incidence rates
        sim_any_hypo = np.mean([
            (sim_data['hypothyroid_grade'] >= 1).any() 
            for sim_data in sim_results
        ])
        
        sim_grade2_hypo = np.mean([
            (sim_data['hypothyroid_grade'] >= 2).any() 
            for sim_data in sim_results
        ])
        
        # Absolute percentage point errors
        any_error = abs(sim_any_hypo - self.obs_any_hypo)
        grade2_error = abs(sim_grade2_hypo - self.obs_grade2_hypo)
        
        return (any_error + grade2_error) / 2
    
    def _compute_timing_error(self, sim_results: List[pd.DataFrame]) -> float:
        """Compute error in time-to-onset distribution."""
        
        # Extract simulated onset times
        sim_onset_times = []
        
        for sim_data in sim_results:
            grade2_times = sim_data[sim_data['hypothyroid_grade'] >= 2]['time']
            if len(grade2_times) > 0:
                sim_onset_times.append(grade2_times.iloc[0])
        
        if len(sim_onset_times) == 0:
            return 1.0  # Large error if no events
        
        sim_onset_times = np.array(sim_onset_times)
        
        # Compare median and IQR
        sim_median = np.median(sim_onset_times)
        sim_iqr = np.percentile(sim_onset_times, [25, 75])
        
        median_error = abs(sim_median - self.obs_median_onset) / self.obs_median_onset
        iqr_error = np.mean(abs(sim_iqr - self.obs_onset_iqr) / self.obs_onset_iqr)
        
        return (median_error + iqr_error) / 2

class GPCalibrator:
    """
    Gaussian Process-based parameter calibration using Bayesian optimization.
    
    Uses scikit-optimize for efficient exploration of parameter space.
    """
    
    def __init__(self, 
                 calibration_data: CalibrationData,
                 n_calls: int = 50,
                 random_state: int = 42):
        """
        Initialize GP calibrator.
        
        Args:
            calibration_data: Training dataset
            n_calls: Number of optimization iterations
            random_state: Random seed for reproducibility
        """
        
        if not HAS_SKOPT:
            raise ImportError("scikit-optimize required for GP calibration")
        
        self.data = calibration_data
        self.n_calls = n_calls
        self.random_state = random_state
        
        # Define parameter search space
        self.space = [
            Real(-3, 0, name='log_k_death'),      # k_death: 0.001 to 1
            Real(1.5, 2.5, name='log_EC50_IFN'),  # EC50_IFN: 30 to 300 pg/mL
            Real(-3, -1, name='log_k_regen'),     # k_regen: 0.001 to 0.1
            Real(0, 2, name='log_epsilon'),       # epsilon: 1 to 100 pg/cell/day
            Real(0, 1, name='log_k_clear_IFN'),   # k_clear_IFN: 1 to 10 day⁻¹
            Real(0, 0.01, name='alpha'),          # alpha: 0 to 0.01
            Real(0.05, 0.5, name='beta'),         # beta: 0.05 to 0.5
            Real(0, 0.2, name='gamma'),           # gamma: 0 to 0.2
        ]
        
        self.objective = ObjectiveFunction(calibration_data)
        
    def optimize(self) -> Tuple[np.ndarray, float]:
        """
        Run Bayesian optimization to find best parameters.
        
        Returns:
            Tuple of (best_parameters, best_objective_value)
        """
        
        @use_named_args(self.space)
        def objective(**params):
            param_vector = np.array(list(params.values()))
            return self.objective(param_vector)
        
        logger.info(f"Starting GP optimization with {self.n_calls} calls...")
        
        result = gp_minimize(
            func=objective,
            dimensions=self.space,
            n_calls=self.n_calls,
            random_state=self.random_state,
            acq_func='EI',  # Expected Improvement
            n_initial_points=10
        )
        
        logger.info(f"Optimization completed. Best objective: {result.fun:.4f}")
        
        # Save results
        self.result = result
        self._save_results()
        
        return result.x, result.fun
    
    def _save_results(self):
        """Save optimization results to file."""
        
        with open('gp_calibration_results.pkl', 'wb') as f:
            pickle.dump(self.result, f)
        
        # Save parameter trace
        param_trace = pd.DataFrame(self.result.x_iters, 
                                  columns=[dim.name for dim in self.space])
        param_trace['objective'] = self.result.func_vals
        param_trace.to_csv('gp_parameter_trace.csv', index=False)
        
        logger.info("Results saved to 'gp_calibration_results.pkl' and 'gp_parameter_trace.csv'")

# Example usage
if __name__ == "__main__":
    # Create synthetic calibration data for demonstration
    np.random.seed(42)
    
    n_patients = 20
    patient_ids = [f"P{i:03d}" for i in range(n_patients)]
    
    # Demographics
    demographics = pd.DataFrame({
        'patient_id': patient_ids,
        'age': np.random.normal(65, 10, n_patients),
        'sex': np.random.choice(['M', 'F'], n_patients),
        'BMI': np.random.normal(27, 4, n_patients)
    })
    
    # Baseline labs
    baseline_labs = pd.DataFrame({
        'patient_id': patient_ids,
        'TSH': np.random.lognormal(0.5, 0.3, n_patients),
        'TPO_Ab': np.random.lognormal(1, 1, n_patients),
        'HLA_DRB1_03': np.random.choice([True, False], n_patients, p=[0.15, 0.85])
    })
    
    # Outcomes (synthetic)
    outcomes = pd.DataFrame({
        'patient_id': patient_ids,
        'any_hypothyroidism': np.random.choice([0, 1], n_patients, p=[0.9, 0.1]),
        'grade2_hypothyroidism': np.random.choice([0, 1], n_patients, p=[0.94, 0.06]),
        'time_to_onset': np.random.exponential(40, n_patients)
    })
    
    # Drug regimens
    drug_regimens = pd.DataFrame({
        'patient_id': patient_ids,
        'drug_type': np.random.choice(['nivolumab', 'pembrolizumab'], n_patients)
    })
    
    # Longitudinal data (sparse)
    longitudinal_data = pd.DataFrame({
        'patient_id': np.repeat(patient_ids[:5], 4),
        'time': np.tile([14, 28, 56, 84], 5),
        'IFN_gamma': np.random.lognormal(2, 1, 20),
        'TSH': np.random.lognormal(1, 0.5, 20)
    })
    
    # Create calibration data object
    cal_data = CalibrationData(
        patient_ids=patient_ids,
        demographics=demographics,
        baseline_labs=baseline_labs,
        longitudinal_data=longitudinal_data,
        outcomes=outcomes,
        drug_regimens=drug_regimens
    )
    
    print("Synthetic calibration data created")
    print(f"- {len(patient_ids)} patients")
    print(f"- {len(longitudinal_data)} longitudinal observations")
    
    # Test objective function
    obj_func = ObjectiveFunction(cal_data)
    
    # Test with default parameters
    default_params = np.array([-3.2, 2.1, -1.5, 0.9, 0.5, 0.0012, 0.15, 0.05])
    obj_value = obj_func(default_params)
    
    print(f"Objective function test: {obj_value:.4f}")
    
    if HAS_SKOPT:
        # Run GP calibration
        calibrator = GPCalibrator(cal_data, n_calls=10)  # Small for demo
        best_params, best_obj = calibrator.optimize()
        
        print(f"Best parameters found:")
        for i, dim in enumerate(calibrator.space):
            print(f"  {dim.name}: {best_params[i]:.4f}")
        print(f"Best objective: {best_obj:.4f}")
    else:
        print("Skipping GP calibration (scikit-optimize not available)")
