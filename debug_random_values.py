import random

# Test what random values are being generated
print('Testing random value generation:')
print('='*50)

# Set fixed seed
random.seed(42)

print('First 20 random values with seed 42:')
for i in range(20):
    val = random.random()
    print(f'Value {i+1:2d}: {val:.6f}')

print('\nFor nivolumab (threshold 0.920):')
random.seed(42)
susceptible_count = 0
for i in range(20):
    val = random.random()
    is_susceptible = val >= 0.920
    if is_susceptible:
        susceptible_count += 1
    print(f'Value {i+1:2d}: {val:.6f} -> {"SUSCEPTIBLE" if is_susceptible else "non-susceptible"}')

print(f'\nSusceptible count: {susceptible_count}/20 ({susceptible_count/20*100:.1f}%)')

# Test with different seed
print('\n' + '='*50)
print('Testing with different seed (123):')

random.seed(123)
susceptible_count = 0
for i in range(20):
    val = random.random()
    is_susceptible = val >= 0.920
    if is_susceptible:
        susceptible_count += 1
    print(f'Value {i+1:2d}: {val:.6f} -> {"SUSCEPTIBLE" if is_susceptible else "non-susceptible"}')

print(f'\nSusceptible count: {susceptible_count}/20 ({susceptible_count/20*100:.1f}%)')
