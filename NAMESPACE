# Generated by roxygen2: do not edit by hand

# Core QSP Model Classes
export(QSPModel)
export(ModelParameters)

# Simulation Functions
export(simulate_patient)
export(calculate_risk_score)
export(classify_hypothyroidism)
export(initial_conditions)

# Population Analysis Classes
export(VirtualCohort)
export(PopulationParameters)
export(RiskStratifier)

# Calibration Classes
export(GPCalibrator)
export(CalibrationData)
export(ObjectiveFunction)

# Analysis Pipeline
export(QSPAnalysisPipeline)
export(run_qsp_analysis)
export(run_interactive_analysis)
export(quick_population_analysis)
export(quick_single_patient)
export(quick_full_analysis)

# Utility Functions
export(drug_concentration)
export(checkpoint_binding)
export(simulate_patients_parallel)
export(sensitivity_analysis)
export(model_diagnostics)
export(batch_simulation)

# Import required packages
import(R6)
import(deSolve)
import(data.table)
importFrom(ggplot2, ggplot, aes, geom_line, geom_point, labs, theme_minimal)
importFrom(dplyr, mutate, filter, select, arrange, group_by, summarise)
importFrom(parallel, detectCores)
importFrom(future, plan, multisession)
importFrom(future.apply, future_lapply)
importFrom(BayesianTools, createBayesianSetup, runMCMC)
importFrom(argparse, ArgumentParser)
importFrom(futile.logger, flog.info, flog.warn, flog.error)
importFrom(survival, Surv, survfit)
importFrom(pracma, trapz)
importFrom(stats, rnorm, runif, rlnorm, rbinom, optim, lm, glm)
importFrom(utils, write.csv, read.csv)
