#' @title Test Drug PK Models
#' @description Unit tests for drug pharmacokinetic models

test_that("DrugPKParameters class works correctly", {
  # Test nivolumab parameters
  nivo_params <- DrugPKParameters$new('nivolumab')
  
  expect_s3_class(nivo_params, "DrugPKParameters")
  expect_equal(nivo_params$drug_name, 'nivolumab')
  expect_equal(nivo_params$clearance, 0.2)
  expect_equal(nivo_params$volume_central, 6.0)
  expect_equal(nivo_params$standard_dose, 240)
  expect_equal(nivo_params$dosing_interval, 14)
  
  # Test pembrolizumab parameters
  pembro_params <- DrugPKParameters$new('pembrolizumab')
  
  expect_equal(pembro_params$drug_name, 'pembrolizumab')
  expect_equal(pembro_params$clearance, 0.22)
  expect_equal(pembro_params$standard_dose, 200)
  expect_equal(pembro_params$dosing_interval, 21)
  
  # Test atezolizumab parameters
  atezo_params <- DrugPKParameters$new('atezolizumab')
  
  expect_equal(atezo_params$drug_name, 'atezolizumab')
  expect_equal(atezo_params$clearance, 0.25)
  expect_equal(atezo_params$standard_dose, 1200)
  
  # Test unknown drug
  expect_error(DrugPKParameters$new('unknown_drug'))
})

test_that("One-compartment PK model works correctly", {
  # Test basic functionality
  time <- c(0, 1, 7, 14, 21)
  dose <- 240  # mg
  clearance <- 0.2  # L/day
  volume <- 6.0  # L
  dosing_times <- c(0, 14)
  
  concentrations <- pk_one_compartment(time, dose, clearance, volume, dosing_times)
  
  expect_true(is.numeric(concentrations))
  expect_equal(length(concentrations), length(time))
  expect_true(all(concentrations >= 0))
  
  # Test that concentration at time 0 is maximum for first dose
  expect_true(concentrations[1] > concentrations[2])
  
  # Test that second dose increases concentration
  expect_true(concentrations[4] > concentrations[3])  # At day 14 vs day 7
  
  # Test single dose
  single_dose_conc <- pk_one_compartment(time, dose, clearance, volume, dosing_times = 0)
  expect_true(all(diff(single_dose_conc) <= 0))  # Should be monotonically decreasing
})

test_that("Two-compartment PK model works correctly", {
  # Test basic functionality
  time <- c(0, 1, 7, 14, 21)
  dose <- 240  # mg
  clearance <- 0.2  # L/day
  volume_central <- 6.0  # L
  volume_peripheral <- 4.2  # L
  intercompartmental_clearance <- 0.8  # L/day
  dosing_times <- c(0, 14)
  
  concentrations <- pk_two_compartment(time, dose, clearance, volume_central, 
                                      volume_peripheral, intercompartmental_clearance, 
                                      dosing_times)
  
  expect_true(is.numeric(concentrations))
  expect_equal(length(concentrations), length(time))
  expect_true(all(concentrations >= 0))
  
  # Test that concentration at time 0 is maximum for first dose
  expect_true(concentrations[1] > concentrations[2])
  
  # Test that second dose increases concentration
  expect_true(concentrations[4] > concentrations[3])  # At day 14 vs day 7
  
  # Test single dose shows bi-exponential decline
  single_dose_conc <- pk_two_compartment(time, dose, clearance, volume_central, 
                                        volume_peripheral, intercompartmental_clearance, 
                                        dosing_times = 0)
  expect_true(all(diff(single_dose_conc) <= 0))  # Should be monotonically decreasing
})

test_that("Dosing schedule generation works correctly", {
  # Test default schedule
  schedule <- generate_dosing_schedule()
  
  expect_true(is.numeric(schedule))
  expect_true(all(schedule >= 0))
  expect_true(all(diff(schedule) > 0))  # Should be increasing
  expect_equal(schedule[1], 0)  # Should start at 0
  
  # Test custom schedule
  custom_schedule <- generate_dosing_schedule(start_time = 0, end_time = 84, dosing_interval = 21)
  
  expect_equal(custom_schedule, c(0, 21, 42, 63, 84))
  
  # Test weekly dosing
  weekly_schedule <- generate_dosing_schedule(start_time = 0, end_time = 28, dosing_interval = 7)
  
  expect_equal(weekly_schedule, c(0, 7, 14, 21, 28))
})

test_that("Drug concentration calculation works correctly", {
  time <- seq(0, 168, by = 7)  # Weekly time points for 24 weeks
  
  # Test nivolumab
  nivo_conc <- calculate_drug_concentration(time, 'nivolumab')
  
  expect_true(is.numeric(nivo_conc))
  expect_equal(length(nivo_conc), length(time))
  expect_true(all(nivo_conc >= 0))
  
  # Test pembrolizumab
  pembro_conc <- calculate_drug_concentration(time, 'pembrolizumab')
  
  expect_true(is.numeric(pembro_conc))
  expect_equal(length(pembro_conc), length(time))
  expect_true(all(pembro_conc >= 0))
  
  # Test atezolizumab
  atezo_conc <- calculate_drug_concentration(time, 'atezolizumab')
  
  expect_true(is.numeric(atezo_conc))
  expect_equal(length(atezo_conc), length(time))
  expect_true(all(atezo_conc >= 0))
  
  # Test dose override
  high_dose_conc <- calculate_drug_concentration(time, 'nivolumab', dose_override = 480)
  normal_dose_conc <- calculate_drug_concentration(time, 'nivolumab', dose_override = 240)
  
  expect_true(all(high_dose_conc >= normal_dose_conc))  # Higher dose should give higher concentrations
  
  # Test two-compartment model
  two_comp_conc <- calculate_drug_concentration(time, 'nivolumab', pk_model = "two_compartment")
  one_comp_conc <- calculate_drug_concentration(time, 'nivolumab', pk_model = "one_compartment")
  
  expect_true(is.numeric(two_comp_conc))
  expect_equal(length(two_comp_conc), length(time))
  expect_true(all(two_comp_conc >= 0))
  
  # Two-compartment should generally give different results than one-compartment
  expect_false(all(abs(two_comp_conc - one_comp_conc) < 1e-6))
})

test_that("PK model parameter validation works correctly", {
  # Test that all supported drugs have valid parameters
  supported_drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  
  for (drug in supported_drugs) {
    params <- DrugPKParameters$new(drug)
    
    # Check that all required parameters are positive
    expect_true(params$clearance > 0)
    expect_true(params$volume_central > 0)
    expect_true(params$volume_peripheral > 0)
    expect_true(params$intercompartmental_clearance > 0)
    expect_true(params$standard_dose > 0)
    expect_true(params$dosing_interval > 0)
    expect_true(params$molecular_weight > 0)
    expect_true(params$protein_binding >= 0 && params$protein_binding <= 1)
    
    # Check that bioavailability is 1.0 for IV drugs
    expect_equal(params$bioavailability, 1.0)
  }
})

test_that("PK model steady-state behavior is correct", {
  # Test that repeated dosing approaches steady state
  time <- seq(0, 168, by = 1)  # Daily time points for 24 weeks
  
  # Calculate concentrations with frequent dosing to approach steady state
  conc <- calculate_drug_concentration(time, 'nivolumab')
  
  # Check that concentrations in the last week are relatively stable
  last_week_conc <- tail(conc, 7)
  cv_last_week <- sd(last_week_conc) / mean(last_week_conc)
  
  # Coefficient of variation should be reasonable (less than 50%)
  expect_true(cv_last_week < 0.5)
  
  # Check that peak-to-trough ratio is reasonable
  peak_trough_ratio <- max(last_week_conc) / min(last_week_conc)
  expect_true(peak_trough_ratio > 1.0)  # Should have some fluctuation
  expect_true(peak_trough_ratio < 10.0)  # But not excessive
})

test_that("PK model handles edge cases correctly", {
  # Test with zero time
  conc_zero <- calculate_drug_concentration(0, 'nivolumab')
  expect_true(is.numeric(conc_zero))
  expect_true(conc_zero >= 0)
  
  # Test with single time point
  conc_single <- calculate_drug_concentration(7, 'nivolumab')
  expect_true(is.numeric(conc_single))
  expect_equal(length(conc_single), 1)
  expect_true(conc_single >= 0)
  
  # Test with custom dosing schedule
  custom_dosing <- c(0, 7, 14, 21)  # Weekly dosing
  time <- seq(0, 28, by = 1)
  conc_custom <- calculate_drug_concentration(time, 'nivolumab', 
                                             dosing_schedule = custom_dosing)
  
  expect_true(is.numeric(conc_custom))
  expect_equal(length(conc_custom), length(time))
  expect_true(all(conc_custom >= 0))
  
  # Test unknown PK model
  expect_error(calculate_drug_concentration(time, 'nivolumab', pk_model = "unknown_model"))
})

test_that("Drug-specific PK parameters are physiologically reasonable", {
  # Test that PK parameters are within expected ranges for monoclonal antibodies
  
  drugs <- c('nivolumab', 'pembrolizumab', 'atezolizumab', 'durvalumab')
  
  for (drug in drugs) {
    params <- DrugPKParameters$new(drug)
    
    # Clearance should be in typical range for mAbs (0.1-0.5 L/day)
    expect_true(params$clearance >= 0.1 && params$clearance <= 0.5)
    
    # Central volume should be close to plasma volume (4-8 L)
    expect_true(params$volume_central >= 4.0 && params$volume_central <= 8.0)
    
    # Peripheral volume should be reasonable (2-10 L)
    expect_true(params$volume_peripheral >= 2.0 && params$volume_peripheral <= 10.0)
    
    # Molecular weight should be typical for mAbs (140-160 kDa)
    expect_true(params$molecular_weight >= 140 && params$molecular_weight <= 160)
    
    # Protein binding should be high for mAbs (>90%)
    expect_true(params$protein_binding >= 0.9)
    
    # Dosing intervals should be reasonable (7-28 days)
    expect_true(params$dosing_interval >= 7 && params$dosing_interval <= 28)
  }
})
