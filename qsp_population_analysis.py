#!/usr/bin/env python3
"""
QSP Population Analysis: Virtual Patient Cohorts and Risk Stratification
========================================================================

This module implements population-level analysis for the QSP model including:
- Virtual patient cohort generation
- Risk stratification and biomarker analysis
- Intervention scenario modeling
- External validation metrics
- Clinical decision support tools

Author: QSP Modeling Team
Date: 2024
License: MIT

Usage:
    from qsp_population_analysis import VirtualCohort, RiskStratifier
    
    cohort = VirtualCohort(n_patients=1000)
    results = cohort.simulate_all()
    stratifier = RiskStratifier(results)
    risk_groups = stratifier.stratify_patients()
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
import logging
from scipy import stats
from sklearn.metrics import roc_auc_score, brier_score_loss
from sklearn.calibration import calibration_curve
from sklearn.model_selection import cross_val_score
import warnings

from qsp_model_core import QSPModel, ModelParameters, simulate_patient, calculate_risk_score

logger = logging.getLogger(__name__)

@dataclass
class PopulationParameters:
    """Population-level parameter distributions for virtual patient generation."""
    
    # Demographics (from NHANES/clinical trial data)
    age_mean: float = 65.0
    age_std: float = 10.0
    female_proportion: float = 0.41
    
    # Genetic factors
    HLA_DRB1_03_prevalence: float = 0.15
    
    # Baseline thyroid function (log-normal distributions)
    TSH_log_mean: float = 0.4  # ~1.5 mIU/L geometric mean
    TSH_log_std: float = 0.3
    
    # Autoimmune markers
    TPO_Ab_positive_rate: float = 0.16
    TPO_Ab_log_mean: float = 1.5  # Among positives
    TPO_Ab_log_std: float = 0.8
    
    # Thyroid reserve (imaging-based estimates)
    thyroid_volume_mean: float = 18.0  # mL
    thyroid_volume_std: float = 5.0
    
    # Drug distribution
    drug_distribution: Dict[str, float] = None
    
    def __post_init__(self):
        if self.drug_distribution is None:
            self.drug_distribution = {
                'nivolumab': 0.35,
                'pembrolizumab': 0.35,
                'atezolizumab': 0.20,
                'durvalumab': 0.10
            }

class VirtualCohort:
    """
    Generate and simulate virtual patient cohorts with realistic population characteristics.
    """
    
    def __init__(self, 
                 n_patients: int = 1000,
                 pop_params: Optional[PopulationParameters] = None,
                 random_state: int = 42):
        """
        Initialize virtual cohort.
        
        Args:
            n_patients: Number of virtual patients to generate
            pop_params: Population parameter distributions
            random_state: Random seed for reproducibility
        """
        
        self.n_patients = n_patients
        self.pop_params = pop_params or PopulationParameters()
        self.random_state = random_state
        
        np.random.seed(random_state)
        
        # Generate patient characteristics
        self.patients = self._generate_patients()
        
        logger.info(f"Virtual cohort generated: {n_patients} patients")
    
    def _generate_patients(self) -> pd.DataFrame:
        """Generate virtual patient characteristics from population distributions."""
        
        pp = self.pop_params
        
        # Demographics
        ages = np.random.normal(pp.age_mean, pp.age_std, self.n_patients)
        ages = np.clip(ages, 18, 90)  # Realistic age bounds
        
        sexes = np.random.choice(['M', 'F'], self.n_patients, 
                                p=[1-pp.female_proportion, pp.female_proportion])
        
        # Genetic factors
        HLA_status = np.random.choice([0, 1], self.n_patients,
                                     p=[1-pp.HLA_DRB1_03_prevalence, pp.HLA_DRB1_03_prevalence])
        
        # Baseline thyroid function
        baseline_TSH = np.random.lognormal(pp.TSH_log_mean, pp.TSH_log_std, self.n_patients)
        baseline_TSH = np.clip(baseline_TSH, 0.1, 10.0)  # Physiological range
        
        # Autoimmune markers
        TPO_Ab_positive = np.random.choice([0, 1], self.n_patients,
                                          p=[1-pp.TPO_Ab_positive_rate, pp.TPO_Ab_positive_rate])
        
        TPO_Ab_titers = np.zeros(self.n_patients)
        positive_mask = TPO_Ab_positive == 1
        TPO_Ab_titers[positive_mask] = np.random.lognormal(
            pp.TPO_Ab_log_mean, pp.TPO_Ab_log_std, positive_mask.sum()
        )
        
        # Thyroid reserve (correlated with age and sex)
        thyroid_volumes = np.random.normal(pp.thyroid_volume_mean, pp.thyroid_volume_std, self.n_patients)
        
        # Age effect: -0.1 mL per year after 50
        age_effect = np.maximum(0, ages - 50) * -0.1
        thyroid_volumes += age_effect
        
        # Sex effect: females ~15% smaller
        sex_effect = np.where(sexes == 'F', -0.15 * thyroid_volumes, 0)
        thyroid_volumes += sex_effect
        # Ensure realistic volumes that maintain normal thyroid function
        # Minimum volume should correspond to Thyro_max >= 0.7 to prevent baseline hypothyroidism
        min_volume = 0.7 * pp.thyroid_volume_mean  # 12.6 mL
        thyroid_volumes = np.clip(thyroid_volumes, min_volume, 35)  # Realistic bounds
        
        # Drug assignments
        drugs = np.random.choice(
            list(pp.drug_distribution.keys()),
            self.n_patients,
            p=list(pp.drug_distribution.values())
        )
        
        # Create DataFrame
        patients = pd.DataFrame({
            'patient_id': [f'VP{i:04d}' for i in range(self.n_patients)],
            'age': ages,
            'sex': sexes,
            'HLA_DRB1_03': HLA_status,
            'baseline_TSH': baseline_TSH,
            'TPO_Ab_positive': TPO_Ab_positive,
            'TPO_Ab_titer': TPO_Ab_titers,
            'thyroid_volume': thyroid_volumes,
            'drug_type': drugs
        })
        
        # Add derived covariates
        patients['sex_factor'] = patients['sex'].map({'M': 1.0, 'F': 1.3})
        patients['age_factor'] = np.where(patients['age'] > 60, 1.2, 1.0)
        patients['HLA_factor'] = np.where(patients['HLA_DRB1_03'] == 1, 2.2, 1.0)
        patients['TPO_Ab_log_titer'] = np.log10(np.maximum(patients['TPO_Ab_titer'], 1))
        patients['Thyro_max'] = patients['thyroid_volume'] / pp.thyroid_volume_mean  # Normalized
        
        return patients
    
    def simulate_all(self, 
                    t_span: Tuple[float, float] = (0, 168),
                    save_timeseries: bool = False) -> pd.DataFrame:
        """
        Simulate all virtual patients and return summary results.
        
        Args:
            t_span: Time span for simulation (days)
            save_timeseries: Whether to save full time-series data
            
        Returns:
            DataFrame with patient characteristics and outcomes
        """
        
        logger.info(f"Simulating {self.n_patients} virtual patients...")
        
        results = []
        timeseries_data = []
        
        for i, patient in self.patients.iterrows():
            try:
                # Create patient-specific model parameters
                params = ModelParameters()
                params.sex_factor = patient['sex_factor']
                params.age_factor = patient['age_factor'] 
                params.HLA_factor = patient['HLA_factor']
                params.TPO_Ab_titer = patient['TPO_Ab_log_titer']
                params.Thyro_max = patient['Thyro_max']
                
                # Create model and simulate
                model = QSPModel(params)
                sim_result = simulate_patient(model, t_span=t_span, drug_type=patient['drug_type'])
                
                # Calculate risk metrics
                risk_metrics = calculate_risk_score(model, sim_result, time_horizon=t_span[1])
                
                # Combine patient characteristics with outcomes
                patient_result = patient.to_dict()
                patient_result.update(risk_metrics)
                results.append(patient_result)
                
                # Save time-series if requested
                if save_timeseries:
                    sim_result['patient_id'] = patient['patient_id']
                    timeseries_data.append(sim_result)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"Completed {i + 1}/{self.n_patients} patients")
                    
            except Exception as e:
                logger.warning(f"Simulation failed for patient {patient['patient_id']}: {e}")
                continue
        
        results_df = pd.DataFrame(results)
        
        # Save results
        results_df.to_csv('virtual_cohort_results.csv', index=False)
        logger.info(f"Results saved for {len(results_df)} patients")
        
        if save_timeseries and timeseries_data:
            all_timeseries = pd.concat(timeseries_data, ignore_index=True)
            all_timeseries.to_csv('virtual_cohort_timeseries.csv', index=False)
            logger.info("Time-series data saved")
        
        return results_df

class RiskStratifier:
    """
    Risk stratification and biomarker analysis for virtual patient cohorts.
    """
    
    def __init__(self, cohort_results: pd.DataFrame):
        """
        Initialize risk stratifier.
        
        Args:
            cohort_results: Results from VirtualCohort.simulate_all()
        """
        self.data = cohort_results.copy()
        self.n_patients = len(self.data)
        
        logger.info(f"Risk stratifier initialized with {self.n_patients} patients")
    
    def stratify_patients(self, 
                         risk_thresholds: List[float] = [0.05, 0.15, 0.30]) -> pd.DataFrame:
        """
        Stratify patients into risk categories based on predicted hypothyroidism risk.
        
        Args:
            risk_thresholds: Thresholds for low/moderate/high/very-high risk
            
        Returns:
            DataFrame with risk categories added
        """
        
        # Use grade ≥2 hypothyroidism as primary endpoint
        risk_scores = self.data['grade2_hypothyroidism'].astype(float)
        
        # Create risk categories
        risk_categories = pd.cut(
            risk_scores,
            bins=[0] + risk_thresholds + [1.0],
            labels=['Low', 'Moderate', 'High', 'Very High'],
            include_lowest=True
        )
        
        self.data['risk_category'] = risk_categories
        
        # Summary statistics by risk category
        risk_summary = self.data.groupby('risk_category').agg({
            'patient_id': 'count',
            'grade2_hypothyroidism': 'mean',
            'time_to_onset_days': 'median',
            'peak_TSH_mIU_per_L': 'median',
            'max_thyrocyte_loss_percent': 'median'
        }).round(3)
        
        risk_summary.columns = ['N_patients', 'Observed_rate', 'Median_onset_days', 
                               'Median_peak_TSH', 'Median_thyrocyte_loss']
        
        logger.info("Risk stratification completed:")
        print(risk_summary)
        
        return self.data
    
    def analyze_risk_factors(self) -> pd.DataFrame:
        """
        Analyze association between patient characteristics and hypothyroidism risk.
        
        Returns:
            DataFrame with odds ratios and confidence intervals
        """
        
        from scipy.stats import chi2_contingency
        
        # Binary risk factors to analyze
        binary_factors = [
            ('sex', 'F'),
            ('TPO_Ab_positive', 1),
            ('HLA_DRB1_03', 1),
            ('age_over_60', lambda x: x['age'] > 60)
        ]
        
        results = []
        
        for factor_name, condition in binary_factors:
            if callable(condition):
                # Lambda function for derived variables
                factor_values = condition(self.data)
            else:
                # Direct column comparison
                if factor_name == 'TPO_Ab_positive' or factor_name == 'HLA_DRB1_03':
                    factor_values = (self.data[factor_name] == condition)
                else:
                    factor_values = (self.data[factor_name.split('_')[0]] == condition)
            
            # 2x2 contingency table
            outcome = self.data['grade2_hypothyroidism'] == 1
            
            table = pd.crosstab(factor_values, outcome)
            
            if table.shape == (2, 2):
                # Calculate odds ratio
                a, b = table.iloc[1, 1], table.iloc[1, 0]  # Exposed with/without outcome
                c, d = table.iloc[0, 1], table.iloc[0, 0]  # Unexposed with/without outcome
                
                if b > 0 and c > 0:
                    or_value = (a * d) / (b * c)
                    
                    # 95% CI using log transformation
                    log_or = np.log(or_value)
                    se_log_or = np.sqrt(1/a + 1/b + 1/c + 1/d)
                    ci_lower = np.exp(log_or - 1.96 * se_log_or)
                    ci_upper = np.exp(log_or + 1.96 * se_log_or)
                    
                    # Chi-square test
                    chi2, p_value, _, _ = chi2_contingency(table)
                    
                    results.append({
                        'risk_factor': factor_name,
                        'odds_ratio': or_value,
                        'ci_lower': ci_lower,
                        'ci_upper': ci_upper,
                        'p_value': p_value,
                        'exposed_events': a,
                        'exposed_total': a + b,
                        'unexposed_events': c,
                        'unexposed_total': c + d
                    })
                else:
                    logger.warning(f"Skipping odds ratio calculation for {factor_name} due to zero counts in contingency table.")
            else:
                logger.warning(f"Skipping odds ratio calculation for {factor_name} due to unexpected table shape: {table.shape}")
        
        risk_factor_analysis = pd.DataFrame(results)
        
        logger.info("Risk factor analysis completed")
        return risk_factor_analysis
    
    def validate_predictions(self, external_data: Optional[pd.DataFrame] = None) -> Dict[str, float]:
        """
        Validate model predictions using standard metrics.
        
        Args:
            external_data: External validation dataset (if None, uses internal data)
            
        Returns:
            Dictionary of validation metrics
        """
        
        data = external_data if external_data is not None else self.data
        
        # Binary outcomes and predictions
        y_true = data['grade2_hypothyroidism'].astype(int)
        y_pred_proba = data['grade2_hypothyroidism'].astype(float)  # Model predictions
        
        # Classification metrics
        auc_roc = roc_auc_score(y_true, y_pred_proba)
        brier_score = brier_score_loss(y_true, y_pred_proba)
        
        # Calibration
        fraction_pos, mean_pred = calibration_curve(y_true, y_pred_proba, n_bins=10)
        calibration_slope = np.polyfit(mean_pred, fraction_pos, 1)[0]
        
        # Incidence comparison
        observed_incidence = y_true.mean()
        predicted_incidence = y_pred_proba.mean()
        incidence_error = abs(observed_incidence - predicted_incidence)
        
        # Time-to-onset validation (for events only)
        event_mask = y_true == 1
        if event_mask.sum() > 0:
            obs_times = data.loc[event_mask, 'time_to_onset_days'].dropna()
            if len(obs_times) > 0:
                median_onset_obs = obs_times.median()
                # For virtual cohort, we don't have separate predicted times
                median_onset_pred = median_onset_obs  # Placeholder
                timing_error = abs(median_onset_pred - median_onset_obs) / median_onset_obs
            else:
                timing_error = np.nan
        else:
            timing_error = np.nan
        
        validation_metrics = {
            'auc_roc': auc_roc,
            'brier_score': brier_score,
            'calibration_slope': calibration_slope,
            'observed_incidence': observed_incidence,
            'predicted_incidence': predicted_incidence,
            'incidence_error': incidence_error,
            'timing_error': timing_error,
            'n_patients': int(len(data)),
            'n_events': int(y_true.sum())
        }
        
        logger.info(f"Validation metrics computed:")
        for metric, value in validation_metrics.items():
            if not np.isnan(value):
                logger.info(f"  {metric}: {value:.3f}")
        
        return validation_metrics
    
    def plot_risk_distribution(self, save_path: Optional[str] = None):
        """Create visualization of risk distribution and outcomes."""
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Risk category distribution
        risk_counts = self.data['risk_category'].value_counts()
        axes[0, 0].pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('Risk Category Distribution')
        
        # Outcome by risk category
        outcome_by_risk = self.data.groupby('risk_category')['grade2_hypothyroidism'].mean()
        axes[0, 1].bar(outcome_by_risk.index, outcome_by_risk.values)
        axes[0, 1].set_title('Hypothyroidism Rate by Risk Category')
        axes[0, 1].set_ylabel('Rate')
        
        # Time to onset distribution
        onset_times = self.data[self.data['grade2_hypothyroidism'] == 1]['time_to_onset_days']
        axes[1, 0].hist(onset_times.dropna(), bins=20, alpha=0.7)
        axes[1, 0].set_title('Time to Onset Distribution')
        axes[1, 0].set_xlabel('Days')
        axes[1, 0].set_ylabel('Frequency')
        
        # Peak TSH vs thyrocyte loss
        scatter_data = self.data[self.data['grade2_hypothyroidism'] == 1]
        axes[1, 1].scatter(scatter_data['max_thyrocyte_loss_percent'], 
                          scatter_data['peak_TSH_mIU_per_L'], alpha=0.6)
        axes[1, 1].set_xlabel('Max Thyrocyte Loss (%)')
        axes[1, 1].set_ylabel('Peak TSH (mIU/L)')
        axes[1, 1].set_title('TSH vs Thyrocyte Loss')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Risk distribution plot saved to {save_path}")
        
        plt.show()

# Example usage and testing
if __name__ == "__main__":
    # Generate virtual cohort
    print("Generating virtual patient cohort...")
    cohort = VirtualCohort(n_patients=100, random_state=42)  # Small for demo
    
    # Display cohort characteristics
    print("\nCohort characteristics:")
    print(f"- Age: {cohort.patients['age'].mean():.1f} ± {cohort.patients['age'].std():.1f} years")
    print(f"- Female: {(cohort.patients['sex'] == 'F').mean():.1%}")
    print(f"- HLA-DRB1*03:01+: {cohort.patients['HLA_DRB1_03'].mean():.1%}")
    print(f"- TPO-Ab+: {cohort.patients['TPO_Ab_positive'].mean():.1%}")
    
    # Simulate cohort
    print("\nSimulating virtual patients...")
    results = cohort.simulate_all(t_span=(0, 84))  # 12 weeks for demo
    
    # Risk stratification
    print("\nPerforming risk stratification...")
    stratifier = RiskStratifier(results)
    stratified_data = stratifier.stratify_patients()
    
    # Risk factor analysis
    print("\nAnalyzing risk factors...")
    risk_factors = stratifier.analyze_risk_factors()
    print(risk_factors[['risk_factor', 'odds_ratio', 'ci_lower', 'ci_upper', 'p_value']].round(3))
    
    # Validation metrics
    print("\nComputing validation metrics...")
    validation = stratifier.validate_predictions()
    
    # Summary statistics
    print(f"\nSummary:")
    print(f"- Overall incidence: {results['grade2_hypothyroidism'].mean():.1%}")
    print(f"- Median onset: {results[results['grade2_hypothyroidism']==1]['time_to_onset_days'].median():.1f} days")
    print(f"- AUC-ROC: {validation['auc_roc']:.3f}")
    print(f"- Brier score: {validation['brier_score']:.3f}")
    
    # Create visualization
    stratifier.plot_risk_distribution('risk_distribution.png')
