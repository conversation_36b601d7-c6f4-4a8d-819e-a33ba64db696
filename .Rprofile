# .Rprofile - Auto-configure Python for this project
# This file automatically sets up Python configuration for the QSPThyroid package

# Set Python path for reticulate
python_path <- file.path(getwd(), ".venv", "Scripts", "python.exe")

if (file.exists(python_path)) {
  Sys.setenv(RETICULATE_PYTHON = python_path)
  cat("✓ Python configured:", python_path, "\n")
} else {
  cat("⚠ Virtual environment not found. Please create it with:\n")
  cat("  python -m venv .venv\n")
  cat("  .venv\\Scripts\\activate\n")
  cat("  pip install numpy pandas scipy matplotlib seaborn\n")
}
